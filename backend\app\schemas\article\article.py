"""
文章相关数据模式
"""
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field


class ArticleBase(BaseModel):
    """文章基础信息"""
    title: str = Field(..., description="文章标题", max_length=255)
    content: str = Field(..., description="文章内容")
    summary: Optional[str] = Field(None, description="文章摘要")
    tags: List[str] = Field(default_factory=list, description="文章标签")
    related_projects: List[str] = Field(default_factory=list, description="关联项目列表")
    image_urls: List[str] = Field(default_factory=list, description="图片地址列表")
    cover_image: Optional[str] = Field(None, description="封面图片")
    keywords: Optional[str] = Field(None, description="SEO关键词")
    is_public: bool = Field(default=True, description="是否公开")
    sort_order: int = Field(default=0, description="排序权重")
    is_top: bool = Field(default=False, description="是否置顶")


class ArticleCreate(ArticleBase):
    """创建文章请求"""
    pass


class ArticleUpdate(BaseModel):
    """更新文章请求"""
    article_id: Optional[str] = Field(None, description="文章id", max_length=255)
    title: Optional[str] = Field(None, description="文章标题", max_length=255)
    content: Optional[str] = Field(None, description="文章内容")
    summary: Optional[str] = Field(None, description="文章摘要")
    tags: Optional[List[str]] = Field(None, description="文章标签")
    related_projects: Optional[List[str]] = Field(None, description="关联项目列表")
    image_urls: Optional[List[str]] = Field(None, description="图片地址列表")
    cover_image: Optional[str] = Field(None, description="封面图片")
    keywords: Optional[str] = Field(None, description="SEO关键词")
    is_public: Optional[bool] = Field(None, description="是否公开")
    sort_order: Optional[int] = Field(None, description="排序权重")
    is_top: Optional[bool] = Field(None, description="是否置顶")
    status: Optional[str] = Field(None, description="文章状态")


class ArticleResponse(ArticleBase):
    """文章响应信息"""
    id: str = Field(..., description="文章ID")
    read_count: int = Field(..., description="阅读量")
    comment_count: int = Field(..., description="评论数")
    like_count: int = Field(..., description="点赞数")
    collect_count: int = Field(..., description="收藏数")
    shared_count_link: int = Field(..., description="分享次数")
    shared_link: Optional[str] = Field(None, description="分享链接")
    return_type: Optional[str] = Field(default="article", description="分享链接")
    status: str = Field(..., description="文章状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    created_by: Optional[str] = Field(None, description="创建人ID")
    # 添加收藏状态字段
    is_collected: Optional[bool] = Field(default=False, description="是否被当前用户收藏")


class ArticleListResponse(BaseModel):
    """文章列表响应"""
    articles: List[ArticleResponse] = Field(..., description="文章列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")


class ArticleLikeRequest(BaseModel):
    """文章点赞请求"""
    article_id: str = Field(..., description="文章ID")
    action: str = Field(..., description="操作：like-点赞，unlike-取消点赞")


class ArticleShareRequest(BaseModel):
    """文章分享请求"""
    article_id: str = Field(..., description="文章ID")
    share_type: str = Field(..., description="分享类型：link-链接分享，qrcode-二维码分享") 