"""
OAuth账号模型转换器
"""
from typing import Optional
from app.models.rbac.oauth import OAuthAccountModel
from app.schemas.rbac.oauth import OAuthAccount, OAuthAccountCreate, OAuthAccountUpdate
from ..base import BaseConverter

class OAuthAccountConverter(BaseConverter[OAuthAccountModel, OAuthAccount]):
    """OAuth账号模型转换器"""
    
    def to_schema(self, model: OAuthAccountModel) -> OAuthAccount:
        """将OAuth账号模型转换为schema
        
        Args:
            model: OAuth账号模型实例
            
        Returns:
            OAuth账号schema实例
        """
        # 临时存储user避免循环引用
        self._store_temp_attr(model, 'user', None)
        
        # 转换基本属性
        oauth_account = OAuthAccount.model_validate(model)
        
        # 恢复user但不转换，避免循环引用
        self._restore_temp_attr(model, 'user')
        
        return oauth_account
    
    def to_model(self, schema: OAuthAccountCreate) -> OAuthAccountModel:
        """将OAuth账号schema转换为模型
        
        Args:
            schema: OAuth账号schema实例
            
        Returns:
            OAuth账号模型实例
        """
        # 转换基本属性
        oauth_account = OAuthAccountModel()
        for field in schema.model_fields:
            if field != 'user' and hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(oauth_account, field, value)
        
        return oauth_account
    
    def update_model(self, model: OAuthAccountModel, schema: OAuthAccountUpdate) -> OAuthAccountModel:
        """使用schema更新模型
        
        Args:
            model: OAuth账号模型实例
            schema: OAuth账号更新schema实例
            
        Returns:
            更新后的OAuth账号模型实例
        """
        # 更新基本属性
        for field in schema.model_fields:
            if field != 'user' and hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(model, field, value)
        
        return model
