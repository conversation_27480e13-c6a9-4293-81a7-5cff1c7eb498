"""Logging filters."""
import logging
import re
from typing import Optional, Dict, Any, Union

class SensitiveDataFilter(logging.Filter):
    """敏感数据过滤器，用于在日志记录中隐藏敏感信息"""
    
    SENSITIVE_FIELDS = {
        'password', 'token', 'secret', 'credit_card',
        'api_key', 'access_token', 'refresh_token'
    }
    
    def __init__(self, additional_fields: Optional[set] = None):
        """
        初始化过滤器
        
        Args:
            additional_fields: 额外的敏感字段集合
        """
        super().__init__()
        self.sensitive_fields = self.SENSITIVE_FIELDS.union(additional_fields or set())
        
    def filter(self, record: logging.LogRecord) -> bool:
        """
        过滤敏感数据
        
        Args:
            record: 日志记录
            
        Returns:
            bool: 总是返回True，允许记录继续处理
        """
        if isinstance(record.msg, dict):
            record.msg = self._filter_dict(record.msg)
        elif isinstance(record.msg, str):
            record.msg = self._filter_string(record.msg)
        
        # 处理 args 中的敏感数据
        if record.args:
            record.args = self._filter_args(record.args)
        
        return True
        
    def _filter_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤字典中的敏感数据
        
        Args:
            data: 要过滤的字典
            
        Returns:
            Dict[str, Any]: 过滤后的字典
        """
        filtered = {}
        for key, value in data.items():
            if any(field in key.lower() for field in self.sensitive_fields):
                filtered[key] = '******'
            elif isinstance(value, dict):
                filtered[key] = self._filter_dict(value)
            elif isinstance(value, (list, tuple, set)):
                filtered[key] = [
                    self._filter_dict(item) if isinstance(item, dict)
                    else item for item in value
                ]
            else:
                filtered[key] = value
        return filtered
        
    def _filter_string(self, text: str) -> str:
        """
        过滤字符串中的敏感数据
        
        Args:
            text: 要过滤的字符串
            
        Returns:
            str: 过滤后的字符串
        """
        for field in self.sensitive_fields:
            if field in text.lower():
                pattern = rf'{field}\s*[=:]\s*[\'"]*[\w\-\.@]+[\'"]*'
                text = re.sub(pattern, f'{field}=******', text, flags=re.IGNORECASE)
        return text
        
    def _filter_args(self, args: Union[tuple, list]) -> Union[tuple, list]:
        """
        过滤参数中的敏感数据
        
        Args:
            args: 要过滤的参数
            
        Returns:
            Union[tuple, list]: 过滤后的参数
        """
        filtered_args = []
        for arg in args:
            if isinstance(arg, dict):
                filtered_args.append(self._filter_dict(arg))
            elif isinstance(arg, str):
                filtered_args.append(self._filter_string(arg))
            elif isinstance(arg, (list, tuple)):
                filtered_args.append(self._filter_args(arg))
            else:
                filtered_args.append(arg)
        
        return tuple(filtered_args) if isinstance(args, tuple) else filtered_args
