"""
OAuth相关配置
"""
from typing import List
from pydantic import Field, field_validator
from .base import BaseAppConfig


class OAuthProviderSettings(BaseAppConfig):
    """OAuth提供商配置基类"""
    
    CLIENT_ID: str = Field(
        default="",
        description="OAuth客户端ID"
    )
    CLIENT_SECRET: str = Field(
        default="",
        description="OAuth客户端密钥"
    )
    REDIRECT_URI: str = Field(
        default="",
        description="OAuth重定向URI"
    )
    SCOPE: List[str] = Field(
        default=[],
        description="OAuth授权范围"
    )
    AUTHORIZE_URL: str = Field(
        default="",
        description="OAuth授权URL"
    )
    TOKEN_URL: str = Field(
        default="",
        description="OAuth令牌URL"
    )
    USERINFO_URL: str = Field(
        default="",
        description="OAuth用户信息URL"
    )
    
    @field_validator("CLIENT_ID", "CLIENT_SECRET", "REDIRECT_URI", "AUTHORIZE_URL", "TOKEN_URL", "USERINFO_URL")
    @classmethod
    def validate_not_empty(cls, v: str) -> str:
        """验证字段不为空"""
        if not v:
            return v
        return v

class WechatOAuthSettings(OAuthProviderSettings):
    """微信OAuth配置"""
    
    PROVIDER_NAME: str = Field(
        default="wechat",
        description="提供商名称"
    )
    AUTHORIZE_URL: str = Field(
        default="https://open.weixin.qq.com/connect/qrconnect",
        description="微信授权URL"
    )
    TOKEN_URL: str = Field(
        default="https://api.weixin.qq.com/sns/oauth2/access_token",
        description="微信令牌URL"
    )
    USERINFO_URL: str = Field(
        default="https://api.weixin.qq.com/sns/userinfo",
        description="微信用户信息URL"
    )
    SCOPE: List[str] = Field(
        default=["snsapi_login"],
        description="微信授权范围"
    )
    USERINFO_URL: str = Field(
        default="https://api.weixin.qq.com/sns/userinfo",
        description="微信用户信息URL"
    )

class DingtalkOAuthSettings(OAuthProviderSettings):
    """钉钉OAuth配置"""
    
    PROVIDER_NAME: str = Field(
        default="dingtalk",
        description="提供商名称"
    )
    AUTHORIZE_URL: str = Field(
        default="https://oapi.dingtalk.com/connect/qrconnect",
        description="钉钉授权URL"
    )
    TOKEN_URL: str = Field(
        default="https://oapi.dingtalk.com/sns/gettoken",
        description="钉钉令牌URL"
    )
    USERINFO_URL: str = Field(
        default="https://oapi.dingtalk.com/sns/getuserinfo",
        description="钉钉用户信息URL"
    )
    SCOPE: List[str] = Field(
        default=["snsapi_login"],
        description="钉钉授权范围"
    )

class FeiShuOAuthSettings(OAuthProviderSettings):
    """飞书OAuth配置"""
    
    PROVIDER_NAME: str = Field(
        default="feishu",
        description="提供商名称"
    )
    AUTHORIZE_URL: str = Field(
        default="https://open.feishu.cn/open-apis/authen/v1/index",
        description="飞书授权URL"
    )
    TOKEN_URL: str = Field(
        default="https://open.feishu.cn/open-apis/authen/v1/access_token",
        description="飞书令牌URL"
    )
    USERINFO_URL: str = Field(
        default="https://open.feishu.cn/open-apis/authen/v1/user_info",
        description="飞书用户信息URL"
    )
    SCOPE: List[str] = Field(
        default=["user"],
        description="飞书授权范围"
    )

class GitHubOAuthSettings(OAuthProviderSettings):
    """GitHub OAuth配置"""

    GITHUB_CLIENT_ID: str = Field(
        default="",  # 设置默认CLIENT_ID
        # # yizhou:
        # default= "Ov23li2VA1rlvU9cDdsX",
        description="GitHub OAuth客户端ID"
    )
    GITHUB_CLIENT_SECRET: str = Field(
        default="",  # 设置默认CLIENT_SECRET
        # yizhou:
        #default="4e0f9a50c51b84688dfe70a3b55353c42ad19bc0",  # 设置默认CLIENT_SECRET
        description="GitHub OAuth客户端密钥"
    )
    GITHUB_REDIRECT_URI: str = Field(
        # default="http://***************:8000/api/v1/oauth/callback/github",  # 设置默认回调地址
        default="",
        # yizhou:
        #default="http://localhost:8080/broadcast/index",
        description="GitHub OAuth重定向URI"
    )

    PROVIDER_NAME: str = Field(
        default="github",
        description="提供商名称"
    )
    AUTHORIZE_URL: str = Field(
        default="https://github.com/login/oauth/authorize",
        description="GitHub授权URL"
    )
    TOKEN_URL: str = Field(
        default="https://github.com/login/oauth/access_token",
        description="GitHub令牌URL"
    )
    USERINFO_URL: str = Field(
        default="https://api.github.com/user",
        description="GitHub用户信息URL"
    )
    SCOPE: List[str] = Field(
        # default=["read:user", "user:email"],
        default=["repo","user"],
        description="GitHub授权范围"
    )

class GiteeOAuthSettings(OAuthProviderSettings):
    """Gitee OAuth配置"""


    # .env
    GITEE_CLIENT_ID: str = Field(
        # default="d62d40ac7d5d252941e251ad5364fbd14f1890e01deee9adb4b0f1465afcee6e",  # 设置默认CLIENT_ID
        #yz
        default="3c2989893f934c5646a6696a1a632491c4633c4bdc4d05fd652ff7153d8a331c",
        description="Gitee OAuth客户端ID"

    )
    GITEE_CLIENT_SECRET: str = Field(
        #default="05420594085fcd204efe7d3f014cb69c9ee6dd06a9f4e83541b32fbf072bc9c9",  # 设置默认CLIENT_SECRET
        # yz
        default="8ac2a827290b4c268ae14831025abca8f5ea89ba303e7d939e3aab792ec06e44",
        description="Gitee OAuth客户端密钥"
    )
    GITEE_REDIRECT_URI: str = Field(
        #default="http://***************:8000/api/v1/oauth/callback/gitee",  # 设置默认回调地址
        default="http://***************/broadcast/index",
        # http://***************/broadcast/index
        description="Gitee OAuth重定向URI"
    )

    PROVIDER_NAME: str = Field(
        default="gitee",
        description="提供商名称"
    )
    AUTHORIZE_URL: str = Field(
        default="https://gitee.com/oauth/authorize",
        description="Gitee授权URL"
    )
    TOKEN_URL: str = Field(
        default="https://gitee.com/oauth/token",
        description="Gitee令牌URL"
    )
    USERINFO_URL: str = Field(
        default="https://gitee.com/api/v5/user",
        description="Gitee用户信息URL"
    )
    SCOPE: List[str] = Field(
        # default=["read:user", "user:email"],
        default=["repo"],
        description="Gitee授权范围"
    )


class OAuthSettings(BaseAppConfig):
    """OAuth总配置"""
    
    ENABLED: bool = Field(
        default=True,
        description="是否启用OAuth认证"
    )
    ENABLED_PROVIDERS: List[str] = Field(
        default=["wechat", "github", "gitee"],
        description="启用的OAuth提供商列表"
    )
    WECHAT: WechatOAuthSettings = Field(
        default_factory=WechatOAuthSettings,
        description="微信OAuth配置"
    )
    GITHUB: GitHubOAuthSettings = Field(
        default_factory=GitHubOAuthSettings,
        description="GitHub OAuth配置"
    )
    GITEE: GiteeOAuthSettings = Field(
        default_factory=GiteeOAuthSettings,
        description="Gitee OAuth配置"
    )


    @field_validator("ENABLED_PROVIDERS")
    @classmethod
    def validate_providers(cls, v: List[str]) -> List[str]:
        """验证提供商列表"""
        valid_providers = ["wechat", "gitee", "github"]
        for provider in v:
            if provider not in valid_providers:
                raise ValueError(f"不支持的OAuth提供商: {provider}")
        return v
