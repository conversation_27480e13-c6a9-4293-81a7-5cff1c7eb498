#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话工具管理API处理器
"""
from typing import List, Dict, Any
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.services.ai_chat import AIChatService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


class ToolsListHandler(BaseHandler):
    """工具列表处理器"""
    
    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def get(self) -> None:
        """获取可用工具列表"""
        try:
            tools_info = self.chat_service.get_available_tools_info()
            
            response_data = {
                "tools": tools_info,
                "total": len(tools_info),
                "tools_enabled": self.chat_service.tools_enabled
            }
            
            self.success_response(response_data, "获取工具列表成功")
            
        except Exception as e:
            logger.error("获取工具列表失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取工具列表失败: {str(e)}")


class MCPServerHandler(BaseHandler):
    """MCP服务器管理处理器"""
    
    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def post(self) -> None:
        """添加MCP服务器"""
        try:
            # 验证请求数据
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")
            
            server_name = self.json_body.get("server_name")
            command = self.json_body.get("command")
            args = self.json_body.get("args", [])
            
            if not server_name or not command:
                raise HTTPError(400, "server_name和command是必需的")
            
            # 添加MCP服务器
            success = await self.chat_service.add_mcp_server(server_name, command, args)
            
            if success:
                # 获取更新后的工具列表
                tools_info = self.chat_service.get_available_tools_info()
                
                response_data = {
                    "server_name": server_name,
                    "success": True,
                    "tools_count": len(tools_info),
                    "tools": tools_info
                }
                
                self.success_response(response_data, f"MCP服务器 {server_name} 添加成功")
            else:
                raise HTTPError(500, f"添加MCP服务器 {server_name} 失败")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("添加MCP服务器失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"添加MCP服务器失败: {str(e)}")
    
    async def delete(self) -> None:
        """断开所有MCP服务器连接"""
        try:
            await self.chat_service.disconnect_mcp_servers()
            
            response_data = {
                "success": True,
                "message": "所有MCP服务器连接已断开"
            }
            
            self.success_response(response_data, "MCP服务器连接断开成功")
            
        except Exception as e:
            logger.error("断开MCP服务器连接失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"断开MCP服务器连接失败: {str(e)}")


class ToolsConfigHandler(BaseHandler):
    """工具配置处理器"""
    
    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def post(self) -> None:
        """配置工具调用"""
        try:
            # 验证请求数据
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")
            
            enabled = self.json_body.get("enabled", True)
            
            # 启用或禁用工具调用
            self.chat_service.enable_tools(enabled)
            
            response_data = {
                "tools_enabled": self.chat_service.tools_enabled,
                "available_tools_count": self.chat_service.mcp_manager.get_tool_count()
            }
            
            self.success_response(response_data, f"工具调用已{'启用' if enabled else '禁用'}")
            
        except Exception as e:
            logger.error("配置工具调用失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"配置工具调用失败: {str(e)}")
    
    async def get(self) -> None:
        """获取工具配置状态"""
        try:
            response_data = {
                "tools_enabled": self.chat_service.tools_enabled,
                "available_tools_count": self.chat_service.mcp_manager.get_tool_count(),
                "mcp_servers_count": self.chat_service.mcp_manager.get_server_count()
            }
            
            self.success_response(response_data, "获取工具配置状态成功")
            
        except Exception as e:
            logger.error("获取工具配置状态失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取工具配置状态失败: {str(e)}")


class ToolCallHandler(BaseHandler):
    """单独工具调用处理器"""
    
    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def post(self, tool_name: str) -> None:
        """直接调用指定工具"""
        try:
            # 验证请求数据
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")
            
            arguments = self.json_body.get("arguments", {})
            
            # 查找工具
            tool = None
            for available_tool in self.chat_service.available_tools:
                if available_tool.name == tool_name:
                    tool = available_tool
                    break
            
            if not tool:
                raise HTTPError(404, f"工具不存在: {tool_name}")
            
            # 执行工具调用
            result = await tool._arun(**arguments)
            
            response_data = {
                "tool_name": tool_name,
                "arguments": arguments,
                "result": result,
                "success": True
            }
            
            self.success_response(response_data, f"工具 {tool_name} 调用成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error(f"工具调用失败 {tool_name}", error=str(e), exc_info=True)
            
            response_data = {
                "tool_name": tool_name,
                "arguments": arguments if 'arguments' in locals() else {},
                "result": str(e),
                "success": False
            }
            
            raise HTTPError(500, f"工具调用失败: {str(e)}")
