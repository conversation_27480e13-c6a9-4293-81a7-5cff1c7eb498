"""
当前用户API处理
"""
from typing import Optional

import jwt
import structlog
from app.core.config import settings
from dependency_injector.wiring import inject, Provide
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.github import GitHubProjectService
from app.services.rbac.auth import AuthService
from app.services.rbac.user import UserService
from app.schemas.rbac.user import UserUpdateRequest, UserChangePasswordRequest, User, CurrentUserUpdateRequest
from app.core.middleware import auth_middleware, require_auth
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)

class CurrentUserHandler(BaseHandler):
    """当前用户信息处理器"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service],
        auth_service: AuthService = Provide[Container.auth_service]

    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service: UserService = user_service
        self.auth_service: AuthService = auth_service
    
    @require_auth(required=True)
    async def get(self):
        """获取当前用户信息"""
        try:
            # 从认证中获取用户ID
            auth_header = self.request.headers.get('Authorization')
            if not auth_header or not auth_header.startswith('Bearer '):
                self.write_error(401, error_message="未授权")
                return
            token = auth_header[7:]  # 移除'Bearer '前缀
            # 获取用户信息


            payload = jwt.decode(
                token,
                settings.security.JWT_SECRET,
                algorithms=[settings.security.JWT_ALGORITHM]
            )
            if payload.get("type") != "access":
                raise ValueError("无效的访问令牌")

            user_id = payload.get("sub")
            if not user_id:
                raise ValueError("无效的访问令牌")


            user_info = await self.user_service.get_user_with_roles(user_id, is_current_user_need_password=True)
            if not user_info:
                self.write_error(500, error_message="用户不存在")
                return
            self.success_response(user_info)
        except Exception as e:
            logger.error("获取当前用户信息失败", error=str(e), exc_info=True)
            self.write_error(500, error_message="获取当前用户信息失败: {str(e)}")
    
    @require_auth(required=True)
    async def put(self):
        """更新当前用户信息"""
        try:
            try:
                auth_header = self.request.headers.get('Authorization')
                if not auth_header or not auth_header.startswith('Bearer '):
                    self.write_error(401, error_message="未授权")
                    return
                token = auth_header[7:]  # 移除'Bearer '前缀
                # 获取用户信息

                payload = jwt.decode(
                    token,
                    settings.security.JWT_SECRET,
                    algorithms=[settings.security.JWT_ALGORITHM]
                )
                if payload.get("type") != "access":
                    raise ValueError("无效的访问令牌")

                user_id = payload.get("sub")
                if not user_id:
                    raise ValueError("无效的访问令牌")

            except Exception as e:
                self.write_error(500, error_message="解析访问令牌失败"+str(e))
                return

            data = self.json_body
            update_data = CurrentUserUpdateRequest.model_validate(data)
            # 更新用户信息
            updated_user = await self.user_service.update_current_user(
                user_id,
                update_data
            )
            
            # if not updated_user:
            #     self.send_error(404, message="用户不存在")
            #     return
            
            # 返回更新后的用户信息
            # self.write(updated_user.model_dump())
            self.success_response()
        except ValueError as e:
            logger.error("更新当前用户信息失败", error=str(e), exc_info=True)
            self.write_error(500, error_message="更新当前用户信息失败" + str(e))
            return
        except Exception as e:
            logger.error("更新当前用户信息失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"更新当前用户信息失败: {str(e)}")
        


class CurrentUserChangePasswordHandler(BaseHandler):
    """当前用户密码修改处理器（暂时禁用）6.4日不再需要原密码改为验证码登陆"""
    
    @inject
    def initialize(
        self,
        user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器
        
        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service: UserService = user_service
    
    @require_auth(required=True)
    async def post(self):
        try:

            # 解析请求体
            try:
                auth_header = self.request.headers.get('Authorization')
                user_id = get_current_user_id(auth_header)
                data = self.json_body
                data["user_id"] = user_id
                password_data = UserChangePasswordRequest.model_validate(data)
            except Exception as e:
                self.send_error(400, message=f"无效的请求数据: {str(e)}")
                return
            
            # 修改密码
            await self.user_service.change_password(
                password_data
            )

            # 返回成功消息
            self.success_response(data=data,message="密码修改成功")
        except ValueError as e:
            logger.error("修改当前用户密码失败", error=str(e), exc_info=True)
            self.send_error(400, message=str(e))
        except Exception as e:
            logger.error("修改当前用户密码失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"修改当前用户密码失败: {str(e)}")


class CurrentUserSystemLogHandler(BaseHandler):
    """当前用户系统消息"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器

        Args:
            user_service: 用户服务
        """
        super().initialize()
        self.user_service: UserService = user_service

    @require_auth(required=True)
    async def get(self):
        """获取当前用户的系统日志列表"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 获取分页参数
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "10")
            is_read_param = self.get_argument("is_read", None)

            # 解析参数
            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 10
            except ValueError:
                page_size = 10

            # 解析已读状态参数
            is_read = None
            if is_read_param is not None:
                if is_read_param.lower() == 'true':
                    is_read = True
                elif is_read_param.lower() == 'false':
                    is_read = False

            # 获取系统日志列表
            result = await self.user_service.get_user_system_logs(
                user_id=user_id,
                page=page,
                page_size=page_size,
                is_read=is_read
            )

            # 返回结果
            self.success_response(result.model_dump())

        except ValueError as e:
            logger.error("获取系统日志参数错误", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("获取系统日志失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"获取系统日志失败: {str(e)}")

    @require_auth(required=True)
    async def post(self):
        """标记系统日志为已读"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 解析请求数据
            from app.schemas.rbac.system_log import SystemLogReadRequest
            request_data = SystemLogReadRequest.model_validate(self.json_body)

            # 标记日志为已读
            success = await self.user_service.mark_logs_as_read(
                user_id=user_id,
                log_ids=request_data.log_ids
            )

            if success:
                self.success_response(message="标记已读成功")
            else:
                self.write_error(500, error_message="标记已读失败")

        except ValueError as e:
            logger.error("标记已读参数错误", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("标记已读失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"标记已读失败: {str(e)}")

    @require_auth(required=True)
    async def put(self):
        """标记所有系统日志为已读"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 标记所有日志为已读
            success = await self.user_service.mark_all_logs_as_read(user_id=user_id)

            if success:
                self.success_response(message="标记所有已读成功")
            else:
                self.write_error(500, error_message="标记所有已读失败")

        except Exception as e:
            logger.error("标记所有已读失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"标记所有已读失败: {str(e)}")


class GitHubCardSearchHandler(BaseHandler):
    """GitHub项目卡片搜索处理器 - 搜索用户收藏项目中卡片内容"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        super().initialize()
        self.github_project_service = github_project_service

    @require_auth(required=True)
    async def get(self):
        """搜索用户收藏项目中卡片的内容

        查询参数:
        - query: 搜索关键词
        - page: 页码，默认1
        - page_size: 每页大小，默认10
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            if not user_id:
                self.write_error(401, error_message="未授权")
                return

            # 获取搜索参数
            search = self.get_argument("search", "").strip()
            if not search:
                self.write_error(400, error_message="搜索关键词不能为空")
                return

            # 获取分页参数
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "10")

            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 10
            except ValueError:
                page_size = 10

            # 参数校验
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 10

            # 调用服务搜索收藏项目中的卡片内容
            result = await self.github_project_service.search_collected_projects_and_cards(
                user_id=user_id,
                query=search,
                page=page,
                page_size=page_size
            )

            # 返回结果
            self.success_response({
                "items": result.items,
                "total": result.total,
                "page": result.page,
                "page_size": result.page_size,
                "search": search
            })

        except ValueError as e:
            logger.error("搜索收藏卡片内容参数错误", error=str(e), user_id=user_id)
            self.write_error(400, error_message=f"请求参数错误: {str(e)}")
        except Exception as e:
            logger.error("搜索收藏卡片内容失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"搜索收藏卡片内容失败: {str(e)}")