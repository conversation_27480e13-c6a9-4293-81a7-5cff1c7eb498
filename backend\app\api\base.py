"""
基础API处理器
"""
import time
import json
from datetime import datetime
from typing import Any, Optional, Dict
from tornado.web import Request<PERSON><PERSON><PERSON>, HTTPError, StaticFileHandler
import structlog
from app.core.middleware import (
    cors_middleware,
    security_headers_middleware,
    rate_limit_middleware
)
from app.schemas.rbac.user import User
from sqlalchemy.orm import Session

from app.utils.uuid_generator import UUIDGenerator

logger = structlog.get_logger(__name__)

def json_encoder(obj: Any) -> str:
    """自定义JSON编码器
    
    Args:
        obj: 需要序列化的对象
        
    Returns:
        str: 序列化后的字符串
    """
    if isinstance(obj, datetime):
        return obj.strftime('%Y-%m-%d %H:%M:%S.%f')
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")


@cors_middleware()
@security_headers_middleware()
@rate_limit_middleware()
class BaseHandler(RequestHandler):
    """基础请求处理器
    
    所有API处理器都应该继承这个基类，它提供了：
    1. CORS支持
    2. 安全响应头
    3. 速率限制
    4. 结构化日志
    5. 统一的响应格式
    """
    
    def initialize(self) -> None:
        """初始化处理器"""
        super().initialize()
        self._request_start_time = time.time()
        
        # 初始化当前用户
        self.current_user: Optional[User] = None
        self.json_body: Optional[Dict[str, Any]] = None
    
    def set_default_headers(self) -> None:
        """设置默认响应头"""
        self.set_header("Content-Type", "application/json; charset=UTF-8")
    
    async def prepare(self) -> Optional[Dict[str, Any]]:
        """请求预处理"""
        # 记录请求日志
        logger.info(
            "收到请求",
            method=self.request.method,
            uri=self.request.uri,
            remote_ip=self.request.remote_ip,
            headers=dict(self.request.headers),
            query_args=self.request.query_arguments,
            body=None if self.request.headers.get("Content-Type", "").startswith("multipart/form-data") else (
                self.request.body.decode() if self.request.body else None),

        )
        
        # 解析JSON请求体
        # if self.request.body:
        #     try:
        #         self.json_body = json.loads(self.request.body)
        #     except json.JSONDecodeError as e:
        #         logger.warning("JSON解析失败", error=str(e))
        #         self.json_body = None
        # else:
        #     self.json_body = None
        content_type = self.request.headers.get("Content-Type", "")
        if content_type.startswith("multipart/form-data"):
            # 对于文件上传请求，跳过JSON解析
            self.json_body = None
        elif self.request.body:
            try:
                self.json_body = json.loads(self.request.body)
            except json.JSONDecodeError:
                self.json_body = None
        else:
            self.json_body = None

    def on_finish(self) -> None:
        """请求结束处理"""
        # 计算响应时间
        # response_time = time.time() - self._request_start_time
        # # 记录响应日志
        logger.info(
            "请求完成",
            method=self.request.method,
            uri=self.request.uri,
            status_code=self.get_status(),
            # response_time=response_time,
            # response_time_ms=int(response_time * 1000),
        )
        super().on_finish()
    
    def write(self, chunk: Any) -> None:
        """重写write方法，添加自定义JSON编码器
        
        Args:
            chunk: 要写入的数据
        """
        if isinstance(chunk, dict):
            chunk = json.dumps(chunk, default=json_encoder)
            self.set_header("Content-Type", "application/json; charset=UTF-8")
        super().write(chunk)

    def write_error(self, status_code: int,  **kwargs: Dict[str, Any]) -> None:

        """自定义错误响应格式
        data: Any = None , error_message: str = "failed",
        Args:
            status_code: HTTP状态码
            **kwargs: 其他参数
        """
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        
        # error_data = {
        #     "code": status_code,
        #     "message": self._reason,
        #     "data": None
        # }

        error_data = {
            "msg": UUIDGenerator.uuid4(),
            "code": status_code,
            "message": kwargs.get("error_message", None),
            "data": kwargs.get("data", None)
        }

        if "exc_info" in kwargs:
            exception = kwargs["exc_info"][1]
            if hasattr(exception, "log_message"):
                error_data["message"] = exception.log_message
            # 记录错误日志
            logger.error(
                "请求处理错误",
                error_type=exception.__class__.__name__,
                error_message=str(exception),
                status_code=status_code,
                uri=self.request.uri,
                method=self.request.method,
                exc_info=exception,
            )
        
        self.finish(error_data)
    
    def success_response(self, data: Any = None, message: str = "success") -> None:
        """统一的成功响应格式
        
        Args:
            data: 响应数据
            message: 成功消息
        """
        self.write({
            "msg": UUIDGenerator.uuid4(),
            "code": 200,
            "message": message,
            "data": data
        })


    def check_xsrf_cookie(self):
        """重写XSRF检查，允许API令牌认证请求跳过XSRF检查"""
        # 如果是OPTIONS请求，跳过检查
        if self.request.method == "OPTIONS":
            return
            
        # 如果请求包含有效的认证令牌，跳过XSRF检查
        auth_header = self.request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return
            
        # 从请求头获取XSRF token
        token = (
            self.request.headers.get("X-Xsrf-Token") or
            self.get_argument("_xsrf", None)
        )
        
        if not token and self.request.method not in {"GET", "HEAD", "OPTIONS"}:
            raise HTTPError(403, "'_xsrf' argument missing from request")
            
        if token:
            _, token, _ = self._decode_xsrf_token(token)
            _, expected_token, _ = self._get_raw_xsrf_token()
            if not token == expected_token:
                raise HTTPError(403, "XSRF token does not match")


# # 图片跨域 详见router.py引用
# class CORSStaticFileHandler(StaticFileHandler):
#     def set_default_headers(self):
#         self.set_header("Access-Control-Allow-Origin", "*")  # 你的前端域名


class CORSStaticFileHandler(StaticFileHandler):
    def prepare(self):
        """在请求处理前调用"""
        logger.info(
            "Preparing request",
            method=self.request.method,
            headers=dict(self.request.headers),
            uri=self.request.uri
        )
        return super().prepare()

    def set_default_headers(self):
        """设置默认的 CORS 头"""
        logger.info("Setting default headers")
        super().set_default_headers()
        # 添加 CORS 头
        self._set_cors_headers()

    def _set_cors_headers(self):
        """设置 CORS 头的辅助方法"""
        logger.info("Setting CORS headers")
        self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Methods", "GET, OPTIONS")
        self.set_header("Access-Control-Allow-Headers",
                        "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization")
        self.set_header("Access-Control-Expose-Headers", "Content-Length,Content-Range")

    def options(self, *args, **kwargs):
        """处理 OPTIONS 请求"""
        logger.info("Handling OPTIONS request")
        self._set_cors_headers()
        self.set_status(204)
        self.set_header("Access-Control-Max-Age", "1728000")
        self.finish()

    async def get(self, *args, **kwargs):
        """处理 GET 请求"""
        logger.info("Handling GET request")
        self._set_cors_headers()

        # 强制不使用缓存
        self.set_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.set_header('Pragma', 'no-cache')
        self.set_header('Expires', '0')

        # 调用父类的 get 方法
        await super().get(*args, **kwargs)

    def write_error(self, status_code: int, **kwargs):
        """处理错误响应"""
        logger.info("Writing error response", status_code=status_code)
        self._set_cors_headers()
        super().write_error(status_code, **kwargs)

    def _write_buffer(self, data, start=None, end=None):
        """重写 _write_buffer 方法"""
        logger.info("Writing buffer")
        self._set_cors_headers()
        super()._write_buffer(data, start, end)

    def finish(self, chunk=None):
        """重写 finish 方法"""
        logger.info("Finishing response")
        self._set_cors_headers()
        super().finish(chunk)

    def compute_etag(self):
        """禁用 ETag"""
        return None