from pydantic import Field

from .base import BaseAppConfig

class EmailSettings(BaseAppConfig):
    """邮箱配置"""
    EMAIL_HOST: str = Field(
        default="smtp.qiye.aliyun.com",
        description="SMTP服务器地址"
    )
    EMAIL_PORT: int = Field(
        default=465,
        description="SMTP端口号"
    )
    EMAIL_USER: str = Field(
        default="<EMAIL>",
        description="发件人邮箱"
    )
    EMAIL_PASSWORD: str = Field(
        default="lPhW7M2MYz3hfElZ",
        description="发件人邮箱密码"
    )
    EMAIL_USE_SSL: bool = Field(
        default=True,
        description="是否使用SSL"
    )
    EMAIL_TIMEOUT: int = Field(
        default=10,
        description="邮件发送超时时间（秒）"
    )