#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/8/1 13:06
# @File    : comment.py
# @Description: 
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from pydantic.v1 import validator


class CommentBase(BaseModel):
    """评论基础模型"""
    content: str = Field(..., min_length=1, max_length=1000, description="评论内容")
    parent_id: Optional[str] = Field(None, description="父评论ID")
    project_id: Optional[str] = Field(None, description="项目ID")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="扩展信息")


    @validator('parent_id', pre=True)
    def validate_parent_id(cls, v):
        """验证parent_id，将空字符串转换为None"""
        if isinstance(v, str) and not v.strip():
            return None
        return v

class CommentCreate(CommentBase):
    """创建评论请求"""
    pass


class CommentUpdate(BaseModel):
    """更新评论请求"""
    content: str = Field(..., min_length=1, max_length=1000, description="评论内容")


class CommentAuditRequest(BaseModel):
    """评论审核请求"""
    status: str = Field(..., description="审核状态：approved-通过，rejected-拒绝")
    audit_reason: Optional[str] = Field(None, description="审核原因")
    comment_id: Optional[str] = Field(None, description="评论id")


class CommentResponse(CommentBase):
    """评论响应模型"""
    id: str = Field(..., description="评论ID")
    article_id: str = Field(..., description="文章ID")
    root_id: Optional[str] = Field(None, description="根评论ID")
    user_id: str = Field(..., description="用户ID")
    user_name: Optional[str] = Field(None, description="用户名称")
    user_avatar: Optional[str] = Field(None, description="用户头像")
    
    # 父评论用户信息 - 新添加的字段
    parent_user_id: Optional[str] = Field(None, description="父评论用户ID")
    parent_user_name: Optional[str] = Field(None, description="父评论用户名称")
    
    like_count: int = Field(0, description="点赞数")
    reply_count: int = Field(0, description="回复数")
    status: str = Field(..., description="评论状态")
    is_public: bool = Field(True, description="是否公开")
    audit_result: Optional[str] = Field(None, description="审核结果")
    audit_reason: Optional[str] = Field(None, description="审核原因")
    audit_time: Optional[datetime] = Field(None, description="审核时间")
    audit_by: Optional[str] = Field(None, description="审核人ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    created_by: Optional[str] = Field(None, description="创建人ID")

    # 嵌套回复
    replies: Optional[List['CommentResponse']] = Field(default_factory=list, description="回复列表")

    class Config:
        from_attributes = True


class CommentTreeResponse(BaseModel):
    """评论树响应模型"""
    article_id: str = Field(..., description="文章ID")
    comments: List[CommentResponse] = Field(..., description="评论列表")
    total: int = Field(..., description="评论总数")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页大小")
    # 新增的参数
    depth: int = Field(3, description="评论树深度")
    comment_id: Optional[str] = Field(None, description="指定的评论ID")


# ==================== 搜索相关的Schema ====================

class CommentSearchRequest(BaseModel):
    """评论搜索请求"""
    project_id: str = Field(..., description="项目ID")
    query: Optional[str] = Field(None, description="搜索关键词")
    user_name: Optional[str] = Field(None, description="用户名")
    status: Optional[str] = Field("approved", description="评论状态")
    is_public: Optional[bool] = Field(True, description="是否公开")
    parent_id: Optional[str] = Field(None, description="父评论ID（null表示根评论）")
    date_from: Optional[datetime] = Field(None, description="开始日期")
    date_to: Optional[datetime] = Field(None, description="结束日期")
    sort_by: Optional[str] = Field("created_at", description="排序字段")
    sort_order: Optional[str] = Field("desc", description="排序方式：asc/desc")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(10, ge=1, le=100, description="每页大小")


class CommentSearchResponse(BaseModel):
    """评论搜索响应"""
    comments: List['CommentResponse'] = Field(..., description="评论列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    total_pages: int = Field(..., description="总页数")
    query: Optional[str] = Field(None, description="搜索关键词")


# 解决循环引用
CommentResponse.model_rebuild()
CommentSearchResponse.model_rebuild()

class CommentDeleteRequest(BaseModel):
    """删除评论请求"""
    comment_id: str = Field(..., description="要删除的评论ID")
    delete_type: str = Field("soft", description="删除类型：soft-软删除，hard-硬删除")
    cascade: bool = Field(True, description="是否级联删除子评论")
