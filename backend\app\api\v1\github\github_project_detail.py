"""
GitHub项目详情API处理器
"""
import structlog
from tornado.web import HTTPError

from app.api.base import <PERSON>Handler
from app.schemas.github.github_project import GitHubProjectCreate, GitHubProjectBase, GitHubProject
from app.services.github.github_project import GitHubProjectService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth

logger = structlog.get_logger(__name__)

class GitHubProjectDetailHandler(BaseHandler):
    """GitHub项目详情处理器"""
    
    @inject
    def initialize(
        self,
        github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器
        
        Args:
            github_project_service: GitHub项目服务实例
        """
        super().initialize()
        self.github_project_service = github_project_service
    
    # @require_auth(required=True, permissions=["github:project:view"])
    async def get(self, project_id: str) -> None:
        try:
            # 获取项目详情
            project = await self.github_project_service.get_project(project_id)
            if not project:
                self.write_error(500, error_message=f"项目不存在: {project_id}")
            # 返回结果
            self.success_response(project.dict())
        except Exception as e:
            logger.error("获取GitHub项目详情时发生错误", error=str(e), project_id=project_id)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")
    
    # @require_auth(required=True, permissions=["github:project:update"])
    async def put(self) -> None:
        """更新GitHub项目信息
        
        Args:
            project_id: 项目ID
        """
        try:
            project_id = self.get_query_argument("project_id", None)
            if not project_id:
                self.write_error(500, error_message=f"缺少project_id")
                return
            # 验证请求数据
            if not self.json_body:
                self.write_error(500, error_message=f"请求体不能为空")
            # 获取当前项目检查是否存在

            current_project = await self.github_project_service.get_project(project_id)
            if not current_project:
                self.write_error(500, error_message=f"项目不存在: {project_id}")
            
            # 更新项目
            updated_project = await self.github_project_service.update_project(project_id, self.json_body)
            if not updated_project:
                self.write_error(500, error_message=f"更新项目失败")
            
            # 返回结果
            self.success_response(updated_project.dict(), message="项目更新成功")
        except ValueError as e:
            logger.error("更新GitHub项目参数错误", error=str(e), project_id=project_id)
            self.write_error(500, error_message=f"请求参数错误: {str(e)}")
        except Exception as e:
            logger.error("更新GitHub项目时发生错误", error=str(e), project_id=project_id)
            self.write_error(500, error_message=f"服务器内部错误" +  str(e))
    
    # @require_auth(required=True, permissions=["github:project:delete"])
    async def delete(self) -> None:
        """删除GitHub项目
        
        Args:
            project_id: 项目ID
        """
        try:
            # 获取当前项目检查是否存在
            project_id = self.get_query_argument("project_id", None)
            current_project = await self.github_project_service.get_project(project_id)
            if not current_project:
                self.write_error(500, error_message=f"项目不存在: {str(project_id)}")
            # 删除项目
            success = await self.github_project_service.delete_project(project_id)
            if not success:
                self.write_error(500, error_message=f"删除项目失败")
            self.success_response(message="项目删除成功")
        except Exception as e:
            logger.error("删除GitHub项目时发生错误", error=str(e), project_id=project_id)
            raise HTTPError(500, error_message=f"服务器内部错误: {str(e)}")

    async def post(self) -> None:
        """添加GitHub项目"""
        try:
            # 验证请求数据
            data = GitHubProjectCreate.model_validate(self.json_body)

            # 调用服务创建项目
            project = await self.github_project_service.create_project(data)

            # 返回结果
            self.success_response(project.dict(), message="项目添加成功")
        except ValueError as e:
            logger.error("创建GitHub项目参数错误", error=str(e))
            self.write_error(500, error_message=f"创建项目参数错误: {str(e)}")
        except Exception as e:
            logger.error("创建GitHub项目失败", error=str(e))
            self.write_error(500, error_message=f"创建项目失败: {str(e)}")