#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文章收藏数据库模型
"""
from sqlalchemy import Column, String, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.models.model_base import ModelBase


class UserArticleCollectModel(ModelBase):
    """用户收藏文章关系表"""
    __tablename__ = "user_article_collects"

    # 用户ID，外键关联到users表
    user_id = Column(String(64), ForeignKey('users.id'), nullable=False, comment='用户ID')

    # 文章ID，外键关联到articles表
    article_id = Column(String(64), ForeignKey('articles.id'), nullable=False, comment='文章ID')

    # 建立唯一约束，防止用户重复收藏同一篇文章
    __table_args__ = (
        UniqueConstraint('user_id', 'article_id', name='uq_user_article_collect'),
    )

    # 关系定义
    user = relationship("UserModel", lazy="select")
    article = relationship("ArticleModel", lazy="select")

    def __repr__(self):
        return f"<UserArticleCollect user_id={self.user_id} article_id={self.article_id}>"