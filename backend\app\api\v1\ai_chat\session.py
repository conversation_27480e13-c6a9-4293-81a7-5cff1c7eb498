#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话会话管理API处理器
"""
from typing import Optional
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.services.ai_chat import AIChatService, ChatConfig, ModelProvider
from app.schemas.ai_chat import (
    SessionCreateRequestSchema, SessionResponseSchema, SessionListResponseSchema,
    SessionUpdateRequestSchema, ChatConfigSchema
)
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


class SessionHandler(BaseHandler):
    """会话管理处理器"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def post(self) -> None:
        """创建新会话"""
        try:
            # 解析请求数据
            request_data = None
            if self.json_body:
                try:
                    request_data = SessionCreateRequestSchema(**self.json_body)
                except Exception as e:
                    logger.error("请求数据验证失败", error=str(e))
                    raise HTTPError(400, f"请求数据格式错误: {str(e)}")
            
            # 构建聊天配置
            config = None
            if request_data and request_data.config:
                config = ChatConfig(
                    model_provider=ModelProvider(request_data.config.model_provider),
                    model_name=request_data.config.model_name,
                    api_key=request_data.config.api_key,
                    base_url=request_data.config.base_url,
                    max_tokens=request_data.config.max_tokens,
                    temperature=request_data.config.temperature,
                    streaming=request_data.config.streaming,
                    system_prompt=request_data.config.system_prompt,
                    request_timeout=request_data.config.request_timeout,
                    max_retries=request_data.config.max_retries,
                    additional_params=request_data.config.additional_params or {}
                )
            
            # 创建会话
            session = await self.chat_service.create_session(
                config=config,
                user_id=request_data.user_id if request_data else None,
                system_prompt=request_data.system_prompt if request_data else None
            )
            
            # 构建响应数据
            response_data = SessionResponseSchema(
                id=session.id,
                title=session.title,
                message_count=len(session.messages),
                model_provider=session.model_provider.value,
                model_name=session.model_name,
                system_prompt=session.system_prompt,
                max_tokens=session.max_tokens,
                temperature=session.temperature,
                user_id=session.user_id,
                metadata=session.metadata,
                created_at=session.created_at,
                updated_at=session.updated_at
            )
            
            self.success_response(response_data.model_dump(), "会话创建成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("创建会话失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"创建会话失败: {str(e)}")


class SessionListHandler(BaseHandler):
    """会话列表处理器"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def get(self) -> None:
        """获取会话列表"""
        try:
            # 获取查询参数
            user_id = self.get_argument("user_id", None)
            
            # 获取会话列表
            sessions = await self.chat_service.list_sessions(user_id)
            
            # 构建响应数据
            session_list = []
            for session in sessions:
                session_data = SessionResponseSchema(
                    id=session.id,
                    title=session.title,
                    message_count=len(session.messages),
                    model_provider=session.model_provider.value,
                    model_name=session.model_name,
                    system_prompt=session.system_prompt,
                    max_tokens=session.max_tokens,
                    temperature=session.temperature,
                    user_id=session.user_id,
                    metadata=session.metadata,
                    created_at=session.created_at,
                    updated_at=session.updated_at
                )
                session_list.append(session_data.model_dump())
            
            response_data = SessionListResponseSchema(
                sessions=session_list,
                total=len(session_list)
            )
            
            self.success_response(response_data.model_dump(), "获取会话列表成功")
            
        except Exception as e:
            logger.error("获取会话列表失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取会话列表失败: {str(e)}")


class SessionDetailHandler(BaseHandler):
    """会话详情处理器"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def get(self, session_id: str) -> None:
        """获取会话详情"""
        try:
            # 获取会话
            session = await self.chat_service.get_session(session_id)
            if not session:
                raise HTTPError(404, "会话不存在")
            
            # 构建响应数据
            response_data = SessionResponseSchema(
                id=session.id,
                title=session.title,
                message_count=len(session.messages),
                model_provider=session.model_provider.value,
                model_name=session.model_name,
                system_prompt=session.system_prompt,
                max_tokens=session.max_tokens,
                temperature=session.temperature,
                user_id=session.user_id,
                metadata=session.metadata,
                created_at=session.created_at,
                updated_at=session.updated_at,
                messages=[
                    {
                        "id": msg.id,
                        "role": msg.role.value,
                        "content": msg.content,
                        "name": msg.name,
                        "function_call": msg.function_call,
                        "metadata": msg.metadata,
                        "timestamp": msg.timestamp
                    }
                    for msg in session.messages
                ]
            )
            
            self.success_response(response_data.model_dump(), "获取会话详情成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取会话详情失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取会话详情失败: {str(e)}")
    
    async def put(self, session_id: str) -> None:
        """更新会话配置"""
        try:
            # 验证请求数据
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")
            
            # 解析请求数据
            try:
                request_data = SessionUpdateRequestSchema(**self.json_body)
            except Exception as e:
                logger.error("请求数据验证失败", error=str(e))
                raise HTTPError(400, f"请求数据格式错误: {str(e)}")
            
            # 构建聊天配置
            config = ChatConfig(
                model_provider=ModelProvider(request_data.config.model_provider),
                model_name=request_data.config.model_name,
                api_key=request_data.config.api_key,
                base_url=request_data.config.base_url,
                max_tokens=request_data.config.max_tokens,
                temperature=request_data.config.temperature,
                streaming=request_data.config.streaming,
                system_prompt=request_data.config.system_prompt,
                request_timeout=request_data.config.request_timeout,
                max_retries=request_data.config.max_retries,
                additional_params=request_data.config.additional_params or {}
            )
            
            # 更新会话配置
            success = await self.chat_service.update_session_config(session_id, config)
            if not success:
                raise HTTPError(404, "会话不存在")
            
            self.success_response(None, "会话配置更新成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("更新会话配置失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"更新会话配置失败: {str(e)}")
    
    async def delete(self, session_id: str) -> None:
        """删除会话"""
        try:
            # 删除会话
            success = await self.chat_service.delete_session(session_id)
            if not success:
                raise HTTPError(404, "会话不存在")
            
            self.success_response(None, "会话删除成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("删除会话失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"删除会话失败: {str(e)}")


class SessionClearHandler(BaseHandler):
    """会话消息清空处理器"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def post(self, session_id: str) -> None:
        """清空会话消息"""
        try:
            # 清空会话消息
            success = await self.chat_service.clear_session_messages(session_id)
            if not success:
                raise HTTPError(404, "会话不存在")
            
            self.success_response(None, "会话消息清空成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("清空会话消息失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"清空会话消息失败: {str(e)}")
