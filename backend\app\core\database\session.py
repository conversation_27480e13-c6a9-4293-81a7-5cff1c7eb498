"""Database session module."""
from contextlib import asynccontextmanager, contextmanager
from typing import (
    AsyncGenerator, 
    Generator, 
    Callable, 
    Union, 
    TypeVar, 
    Generic, 
    overload,
    Optional,
    cast
)
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
import structlog
from functools import wraps

from .exceptions import DatabaseError

logger = structlog.get_logger(__name__)

T = TypeVar('T', Session, AsyncSession)

def validate_session_factory(factory: Callable[[], T]) -> None:
    """验证会话工厂函数
    
    Args:
        factory: 会话工厂函数
        
    Raises:
        ValueError: 当工厂函数无效时
    """
    if not callable(factory):
        raise ValueError("session_factory must be callable")

class SyncSessionManager:
    """同步数据库会话管理器"""
    
    def __init__(self, session_factory: Callable[[], Session]):
        """初始化同步会话管理器
        
        Args:
            session_factory: 同步会话工厂函数
            
        Raises:
            ValueError: 当会话工厂函数无效时
        """
        validate_session_factory(session_factory)
        self._session_factory = session_factory
        self._current_session: Optional[Session] = None
        
    @property
    def current_session(self) -> Optional[Session]:
        """获取当前会话"""
        return self._current_session
        
    @contextmanager
    def session(self) -> Generator[Session, None, None]:
        """获取同步数据库会话
        
        Yields:
            Session: 数据库会话
            
        Example:
            with session_manager.session() as session:
                result = session.execute(query)
                session.commit()
                
        Raises:
            DatabaseError: 当数据库操作失败时
        """
        if self._current_session is not None:
            yield self._current_session
            return
            
        session: Session = self._session_factory()
        self._current_session = session
        try:
            yield session
            session.commit()
        except SQLAlchemyError as e:
            session.rollback()
            logger.error("Database error",
                        error=str(e),
                        error_type=type(e).__name__,
                        context={"operation": "commit"})
            raise DatabaseError(f"Database error: {str(e)}") from e
        except Exception as e:
            session.rollback()
            logger.error("Unexpected error",
                        error=str(e),
                        error_type=type(e).__name__)
            raise
        finally:
            session.close()
            self._current_session = None
            
    def cleanup(self) -> None:
        """清理资源"""
        if self._current_session is not None:
            self._current_session.close()
            self._current_session = None

class AsyncSessionManager:
    """异步数据库会话管理器"""
    
    def __init__(self, session_factory: Callable[[], AsyncSession]):
        """初始化异步会话管理器
        
        Args:
            session_factory: 异步会话工厂函数
            
        Raises:
            ValueError: 当会话工厂函数无效时
        """
        validate_session_factory(session_factory)
        self._session_factory = session_factory
        self._current_session: Optional[AsyncSession] = None
        
    @property
    def current_session(self) -> Optional[AsyncSession]:
        """获取当前会话"""
        return self._current_session
        
    @asynccontextmanager
    async def session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取异步数据库会话
        
        Yields:
            AsyncSession: 异步数据库会话
            
        Example:
            async with session_manager.session() as session:
                result = await session.execute(query)
                await session.commit()
                
        Raises:
            DatabaseError: 当数据库操作失败时
        """
        # 多个并发操作尝试使用同一会话，异步会话不支持并发操作

        # if self._current_session is not None:
        #     yield self._current_session
        #     return
            
        session: AsyncSession = self._session_factory()
        self._current_session = session
        try:
            yield session
            await session.commit()
        except SQLAlchemyError as e:
            await session.rollback()
            logger.error("Database error",
                        error=str(e),
                        error_type=type(e).__name__,
                        context={"operation": "commit"})
            raise DatabaseError(f"Database error: {str(e)}") from e
        except Exception as e:
            await session.rollback()
            logger.error("Unexpected error",
                        error=str(e),
                        error_type=type(e).__name__)
            raise
        finally:
            await session.close()
            self._current_session = None
            
    async def cleanup(self) -> None:
        """清理资源"""
        if self._current_session is not None:
            await self._current_session.close()
            self._current_session = None

class SessionManager(Generic[T]):
    """数据库会话管理器"""
    
    def __init__(
        self, 
        sync_session_factory: Callable[[], Session],
        async_session_factory: Callable[[], AsyncSession]
    ):
        """初始化会话管理器
        
        Args:
            sync_session_factory: 同步会话工厂函数
            async_session_factory: 异步会话工厂函数
            
        Raises:
            ValueError: 当会话工厂函数无效时
        """
        self._sync_session_manager = SyncSessionManager(sync_session_factory)
        self._async_session_manager = AsyncSessionManager(async_session_factory)
        
    @property
    def sync_session_manager(self) -> SyncSessionManager:
        """获取同步会话管理器"""
        return self._sync_session_manager

    @property
    def async_session_manager(self) -> AsyncSessionManager:
        """获取异步会话管理器"""
        return self._async_session_manager
        
    @overload
    def __call__(self, *, sync: bool = True) -> Generator[Session, None, None]:
        ...
        
    @overload
    def __call__(self, *, sync: bool = False) -> AsyncGenerator[AsyncSession, None]:
        ...
        
    def __call__(
        self, 
        *, 
        sync: bool = False
    ) -> Union[Generator[Session, None, None], AsyncGenerator[AsyncSession, None]]:
        """获取会话
        
        Args:
            sync: 是否使用同步会话
            
        Returns:
            Union[Generator[Session, None, None], AsyncGenerator[AsyncSession, None]]: 会话生成器
            
        Example:
            # 同步用法
            with session_manager(sync=True) as session:
                result = session.execute(query)
                
            # 异步用法
            async with session_manager() as session:
                result = await session.execute(query)
        """
        if sync:
            return cast(Generator[Session, None, None], self._sync_session_manager.session())
        return cast(AsyncGenerator[AsyncSession, None], self._async_session_manager.session())
        
    async def cleanup(self) -> None:
        """清理所有资源"""
        self._sync_session_manager.cleanup()
        await self._async_session_manager.cleanup()
