"""
角色管理API处理器
"""
from typing import List, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.middleware import auth_middleware, require_auth
from app.core.di.containers import Container
from app.services.rbac.role import RoleService
from app.schemas.rbac.role import (
    RoleCreateRequest,
    RoleUpdateRequest,
    Role,
    RoleListResponse,
    RolePermissionRequest,
    RolePermissionGroupRequest
)

logger = structlog.get_logger(__name__)

class RoleHandler(BaseHandler):
    """角色管理处理器"""
    
    @inject
    def initialize(
        self,
        role_service: RoleService = Provide[Container.role_service]
    ) -> None:
        """初始化处理器
        
        Args:
            role_service: 角色服务
        """
        super().initialize()
        self.role_service = role_service

    @require_auth(required=True, permissions=["system:role:delete"])
    async def delete(self) -> None:
        """批量删除角色"""
        try:
            # 验证请求数据
            if not self.json_body or "role_ids" not in self.json_body:
                raise HTTPError(400, "请求必须包含 role_ids 字段")
            role_ids = self.json_body.get("role_ids", [])
            if not isinstance(role_ids, list) or not role_ids:
                raise HTTPError(400, "role_ids 必须是非空数组")
            # 调用服务批量删除角色
            result = await self.role_service.batch_delete(role_ids)
            self.success_response({
                "total": len(role_ids),
                "deleted": result.get("deleted", 0),
                "failed": result.get("failed", 0),
                "errors": result.get("errors", {})
            })
        except ValueError as e:
            logger.error("批量删除角色参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
            return
        except Exception as e:
            logger.error("批量删除角色失败", error=str(e))
            self.write_error(500, error_message="批量删除角色失败" + str(e))
            return

    #here
    @require_auth(required=True, permissions=["system:role:list"])
    async def get(self) -> None:
        """获取角色列表"""
        try:
            # 获取查询参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            search = self.get_argument("search", None)
            # 调用服务获取角色列表
            result = await self.role_service.get_list(
                page=page,
                page_size=page_size,
                search=search
            )
            self.success_response(result.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
            return
        except Exception as e:
            logger.error("获取角色列表失败", error=str(e))
            self.write_error(500, error_message="获取角色列表失败" + str(e))
            return

    @require_auth(required=True, permissions=["system:role:add"])
    async def post(self) -> None:
        """创建角色"""
        try:
            # 验证请求数据
            data = RoleCreateRequest(**self.json_body)
            # 调用服务创建角色
            # role, error = await self.role_service.create(data)
            # if error:
            #     raise HTTPError(400, error)
            role = await self.role_service.create(data)
            self.success_response(role.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
            return
        except Exception as e:
            logger.error("创建角色失败", error=str(e))
            self.write_error(500, error_message="创建角色失败" + str(e))
            return

class RoleDetailHandler(BaseHandler):
    """角色详情处理器"""
    
    @inject
    def initialize(
        self,
        role_service: RoleService = Provide[Container.role_service]
    ) -> None:
        """初始化处理器
        
        Args:
            role_service: 角色服务
        """
        super().initialize()
        self.role_service = role_service
    
    @require_auth(required=True, permissions=["system:role:list"])
    async def get(self, role_id: str) -> None:
        """获取角色详情
        
        Args:
            role_id: 角色ID
        """
        try:
            # 调用服务获取角色详情
            role = await self.role_service.get_all_permissions(role_id=role_id)
            if not role:
                self.write_error(500, error_message="角色不存在")
                return
            self.success_response(role)
        except ValueError as e:
            self.write_error(500, error_message=str(e))

        except Exception as e:
            logger.error("获取角色详情失败", error=str(e))
            self.write_error(500, "获取角色详情失败" + str(e))

    
    @require_auth(required=True, permissions=["system:role:edit_batch"])
    async def put(self, role_id: str) -> None:
        """更新角色
        
        Args:
            role_id: 角色ID
        """
        try:
            # 验证请求数据
            data = RoleUpdateRequest(**self.json_body)
            
            # 调用服务更新角色
            role = await self.role_service.update(request=data)
            if not role:
                self.write_error(500, error_message="角色不存在")
            self.success_response(role.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("更新角色失败", error=str(e))
            self.write_error(500, error_message="更新角色失败" + str(e))
    
    @require_auth(required=True, permissions=["system:role:delete"])
    async def delete(self, role_id: str) -> None:
        """删除角色
        
        Args:
            role_id: 角色ID
        """
        try:
            # 调用服务删除角色
            error = await self.role_service.delete(role_id)
            if error:
                self.write_error(500, "删除失败")
                return
            self.success_response()
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("删除角色失败", error=str(e))
            self.write_error(500, error_message="删除角色失败" + str(e))


class RolePermissionHandler(BaseHandler):
    """角色权限管理处理器"""
    
    @inject
    def initialize(
        self,
        role_service: RoleService = Provide[Container.role_service]
    ) -> None:
        """初始化处理器
        
        Args:
            role_service: 角色服务
        """
        super().initialize()
        self.role_service = role_service

    @require_auth(required=True, permissions=["system:role:add"])
    async def post(self, role_id: str) -> None:
        """添加权限到角色
        
        Args:
            role_id: 角色ID
        """
        try:
            # 验证请求数据
            data = RolePermissionRequest(**self.json_body)
            data.role_id = role_id
            
            # 调用服务添加权限
            role = await self.role_service.add_permissions(data)
            if not role:
                self.write_error(500, "删除失败")
                return
            self.success_response(role.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("添加权限失败", error=str(e))
            self.write_error(500, error_message="删除角色失败" + str(e))
    
    @require_auth(required=True, permissions=["system:role:delete"])
    async def delete(self, role_id: str) -> None:
        """从角色移除权限
        
        Args:
            role_id: 角色ID
        """
        try:
            # 验证请求数据
            data = RolePermissionRequest(**self.json_body)
            data.role_id = role_id
            
            # 调用服务移除权限
            role = await self.role_service.remove_permissions(data)
            if not role:
                self.write_error(500, "角色不存在")
                return
            self.success_response(role.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("移除权限失败", error=str(e))
            self.write_error(500, error_message="移除权限失败" + str(e))


class RolePermissionGroupHandler(BaseHandler):
    """角色权限组管理处理器"""
    
    @inject
    def initialize(
        self,
        role_service: RoleService = Provide[Container.role_service]
    ) -> None:
        """初始化处理器
        
        Args:
            role_service: 角色服务
        """
        super().initialize()
        self.role_service = role_service
    
    @require_auth(required=True, permissions=["system:role:add_permission_group"])
    async def post(self, role_id: str) -> None:
        """添加权限组到角色
        
        Args:
            role_id: 角色ID
        """
        try:
            # 验证请求数据
            data = RolePermissionGroupRequest(
                role_id=role_id,
                permission_group_ids=self.json_body.get("permission_group_ids", [])
            )
            
            # 调用服务添加权限组
            role = await self.role_service.add_permission_groups(data)
            if not role:
                self.write_error(500, "角色不存在")
                return
            self.success_response(role.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("添加权限组失败", error=str(e))
            self.write_error(500, error_message="添加权限组失败" + str(e))

    
    @require_auth(required=True, permissions=["system:role:remove_permission_group"])
    async def delete(self, role_id: str) -> None:
        """从角色移除权限组
        
        Args:
            role_id: 角色ID
        """
        try:
            # 验证请求数据
            data = RolePermissionGroupRequest(
                role_id=role_id,
                permission_group_ids=self.json_body.get("permission_group_ids", [])
            )
            
            # 调用服务移除权限组
            role = await self.role_service.remove_permission_groups(data)
            if not role:
                self.write_error(500, "角色不存在")
            self.success_response(role.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("移除权限组失败", error=str(e))
            self.write_error(500, error_message="移除权限组失败" + str(e))


class UserRoleHandler(BaseHandler):
    """角色管理处理器"""

    @inject
    def initialize(
            self,
            role_service: RoleService = Provide[Container.role_service]
    ) -> None:
        """初始化处理器

        Args:
            role_service: 角色服务
        """
        super().initialize()
        self.role_service = role_service

    # here
    @require_auth(required=True, permissions=["system:user:list"])
    async def get(self) -> None:
        """获取角色列表"""
        try:
            # 获取查询参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            search = self.get_argument("search", None)
            # 调用服务获取角色列表
            result = await self.role_service.get_list(
                page=page,
                page_size=page_size,
                search=search
            )
            self.success_response(result.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("获取角色列表失败", error=str(e))
            self.write_error(500, error_message="获取角色列表失败" + str(e))
