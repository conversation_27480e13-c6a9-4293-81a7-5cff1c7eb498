"""
安全相关配置
"""
from typing import List
from pydantic import Field, field_validator

from .base import BaseAppConfig

class SecuritySettings(BaseAppConfig):
    """安全配置"""
    
    # JWT配置
    JWT_SECRET: str = Field(
        default="your-super-secret-key",
        description="JWT密钥"
    )
    JWT_ALGORITHM: str = Field(
        default="HS256",
        description="JWT算法"
    )
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30,
        description="访问令牌过期时间（分钟）"
    )
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(
        default=10,
        description="刷新令牌过期时间（天）"
    )
    
    # 密码策略
    PASSWORD_MIN_LENGTH: int = Field(
        default=8,
        description="密码最小长度",
        ge=8
    )
    PASSWORD_MAX_LENGTH: int = Field(
        default=32,
        description="密码最大长度",
        le=128
    )
    PASSWORD_REQUIRE_UPPERCASE: bool = Field(
        default=True,
        description="密码是否需要包含大写字母"
    )
    PASSWORD_REQUIRE_LOWERCASE: bool = Field(
        default=True,
        description="密码是否需要包含小写字母"
    )
    PASSWORD_REQUIRE_DIGITS: bool = Field(
        default=True,
        description="密码是否需要包含数字"
    )
    PASSWORD_REQUIRE_SPECIAL_CHARS: bool = Field(
        default=True,
        description="密码是否需要包含特殊字符"
    )
    
    # 会话安全
    SESSION_EXPIRE_MINUTES: int = Field(
        default=60 * 24,  # 24 小时
        description="会话过期时间（分钟）",
        ge=1
    )
    MAX_FAILED_LOGIN_ATTEMPTS: int = Field(
        default=5,
        description="最大登录失败次数",
        ge=1
    )
    ACCOUNT_LOCKOUT_MINUTES: int = Field(
        default=15,
        description="账户锁定时间（分钟）",
        ge=1
    )
    
    # CORS 配置
    CORS_ALLOW_ORIGINS: List[str] = Field(
        default=["*"],
        description="允许的跨域来源"
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(
        default=True,
        description="是否允许跨域携带凭证"
    )
    CORS_ALLOW_METHODS: List[str] = Field(
        default=["*"],
        description="允许的跨域方法"
    )
    CORS_ALLOW_HEADERS: List[str] = Field(
        default=["*"],
        description="允许的跨域请求头"
    )
    
    # 速率限制配置
    RATE_LIMIT_ENABLED: bool = Field(
        default=True,
        description="是否启用速率限制"
    )
    RATE_LIMIT_REQUESTS: int = Field(
        default=1000,
        description="时间窗口内允许的最大请求数"
    )
    RATE_LIMIT_PERIOD: int = Field(
        default=60,
        description="时间窗口（秒）"
    )
    
    # HSTS配置
    HSTS_ENABLED: bool = Field(
        default=True,
        description="是否启用HSTS"
    )
    HSTS_MAX_AGE: int = Field(
        default=31536000,
        description="HSTS过期时间（秒）"
    )
    
    # 安全头部
    SECURITY_HEADERS: bool = Field(
        default=True,
        description="是否启用安全响应头"
    )
    
    @field_validator("RATE_LIMIT_REQUESTS")
    def validate_rate_limit_requests(cls, v: int) -> int:
        """验证速率限制请求数"""
        if v < 1:
            raise ValueError("速率限制请求数必须大于0")
        return v
    
    @field_validator("RATE_LIMIT_PERIOD")
    def validate_rate_limit_period(cls, v: int) -> int:
        """验证速率限制时间窗口"""
        if v < 1:
            raise ValueError("速率限制时间窗口必须大于0")
        return v
    
    @field_validator("HSTS_MAX_AGE")
    def validate_hsts_max_age(cls, v: int) -> int:
        """验证HSTS过期时间"""
        if v < 0:
            raise ValueError("HSTS过期时间必须大于等于0")
        return v
    
    @field_validator("JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    def validate_jwt_expire_minutes(cls, v: int) -> int:
        """验证JWT过期时间"""
        if v < 1:
            raise ValueError("JWT过期时间必须大于0")
        return v
