from pydantic import Field

from .base import BaseAppConfig


class PhoneSettings(BaseAppConfig):
    """手机验证码配置"""

    # 验证码配置
    PHONE_TIMEOUT: int = Field(
        default=10,
        description="验证码有效期（分钟）"
    )

    # 容联云配置
    RONGLIAN_ACCOUNT_SID: str = Field(
        default="",
        description="容联云账号SID"
    )

    RONGLIAN_ACCOUNT_TOKEN: str = Field(
        default="",
        description="容联云账号Token"
    )

    RONGLIAN_APP_ID: str = Field(
        default="",
        description="容联云应用ID"
    )

    RONGLIAN_TEMPLATE_ID: str = Field(
        default="1",
        description="容联云短信模板ID"
    )

    RONGLIAN_BASE_URL: str = Field(
        default="",
        description="容联云API基础URL"
    )

    # 生产环境URL
    RONGLIAN_PRODUCTION_URL: str = Field(
        default="",
        description="容联云生产环境API基础URL"
    )

    # 短信发送限制配置
    SMS_SEND_INTERVAL: int = Field(
        default=60,
        description="同一手机号发送短信的最小间隔（秒）"
    )

    SMS_DAILY_LIMIT: int = Field(
        default=10,
        description="同一手机号每日发送短信的最大次数"
    )

    SMS_IP_LIMIT: int = Field(
        default=100,
        description="同一IP每日发送短信的最大次数"
    )

