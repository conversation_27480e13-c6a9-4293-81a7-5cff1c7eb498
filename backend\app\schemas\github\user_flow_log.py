#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/28 17:50
# @File    : user_flow_log.py
# @Description: 
"""
from pydantic import BaseModel, Field
from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional


class UserFlowLog(BaseModel):
    """用户流程日志响应模型"""
    id: str = Field(..., description="日志ID")
    log_type: str = Field(..., description="日志类型")
    project_id: str = Field(..., description="日志关联项目id")
    project_name: str = Field(..., description="日志关联项目名字")
    content: str = Field(..., description="日志内容")
    created_at: datetime = Field(..., description="创建时间")
    created_by: Optional[str] = Field(None, description="创建人ID")

    class Config:
        """配置"""
        from_attributes = True


class UserFlowLogList(BaseModel):
    """用户流程日志列表响应模型"""
    logs: List[UserFlowLog] = Field(..., description="日志列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")