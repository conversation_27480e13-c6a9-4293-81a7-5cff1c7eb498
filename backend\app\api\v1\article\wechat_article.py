#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/31 13:06
# @File    : wechat_article.py
# @Description: 
"""

import structlog
from tornado.web import HTTPError
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.utils.wechat_articles_util import WechatArticlesUtil
from app.core.middleware import auth_middleware, require_auth

logger = structlog.get_logger(__name__)


class WechatArticlesHandler(BaseHandler):
    """微信公众号文章处理器"""

    def initialize(self):
        super().initialize()

    # @require_auth
    async def get(self):
        """
        查询参数:
        - account_type: 账号类型 (mp/oa) - mp表示公众号，oa表示服务号
        - article_type: 文章类型 (published/draft/all)
        - page: 页码 (默认1)
        - page_size: 每页数量 (默认10, 最大20)
        - include_content: 是否包含内容 (默认false)
        """
        try:
            # 获取查询参数
            account_type = self.get_argument("account_type", "mp")
            article_type = self.get_argument("article_type", "all")
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            include_content = self.get_argument("include_content", "false").lower() == "true"

            # 参数验证
            if account_type not in ["mp", "oa"]:
                raise HTTPError(400, "account_type参数必须是 mp 或 oa")
            if article_type not in ["published", "draft", "all"]:
                raise HTTPError(400, "article_type参数必须是 published、draft 或 all")

            if page < 1:
                raise ValueError("page参数必须大于等于1")
            if page_size < 1 or page_size > 20:
                raise ValueError("page_size参数必须在1-20之间")

            logger.info("获取微信公众号文章",
                        account_type=account_type,
                        article_type=article_type,
                        page=page,
                        page_size=page_size,
                        include_content=include_content)

            # 根据文章类型获取数据
            if article_type == "published":
                result = WechatArticlesUtil.get_published_articles(account_type, page, page_size, include_content)
            elif article_type == "draft":
                result = WechatArticlesUtil.get_draft_articles(account_type, page, page_size, include_content)
            else:  # all
                result = WechatArticlesUtil.get_all_articles(account_type, page, page_size, include_content)

            if result["success"]:
                self.success_response(result["data"], message=result["message"])
            else:
                self.write_error(500, error_message=result["message"])
        except Exception as e:
            logger.error("获取文章失败", error=str(e))
            self.write_error(500, error_message=f"获取文章失败")


class WechatArticleDetailHandler(BaseHandler):
    """微信公众号文章详情处理器"""

    def initialize(self):
        super().initialize()

    # @require_auth
    async def get(self, article_id: str):
        """获取文章详情

        Args:
            article_id: 文章ID
        """
        try:
            # 获取查询参数
            account_type = self.get_argument("account_type", "mp")
            article_type = self.get_argument("article_type", "published")

            # 参数验证
            if account_type not in ["mp", "oa"]:
                self.write_error(500, error_message=f"account_type参数必须是 mp 或 oa")
                return
            if article_type not in ["published", "draft"]:
                self.write_error(500, error_message=f"article_type参数必须是 published 或 draft")
                return
            if not article_id:
                self.write_error(500, error_message=f"article_id参数不能为空")
                return
            logger.info("获取文章详情",
                        article_id=article_id,
                        account_type=account_type,
                        article_type=article_type)

            # 获取文章详情
            result = WechatArticlesUtil.get_article_detail(article_id, account_type, article_type)

            if result["success"]:
                self.success_response(result["data"], message=result["message"])
            else:
                self.write_error(500, error_message=result["message"])
                return
        except Exception as e:
            logger.error("获取文章详情失败",
                         article_id=article_id,
                         error=str(e))
            self.write_error(500, error_message=result["message"])
            return


class WechatArticlesStatsHandler(BaseHandler):
    """微信公众号文章统计处理器"""

    def initialize(self):
        super().initialize()

    # @require_auth
    async def get(self):
        """获取文章统计信息"""
        try:
            # 获取查询参数
            account_type = self.get_argument("account_type", "mp")

            # 参数验证
            if account_type not in ["mp", "oa"]:
                raise HTTPError(400, "account_type参数必须是 mp 或 oa")

            logger.info("获取文章统计信息", account_type=account_type)

            # 获取已发布文章统计
            published_result = WechatArticlesUtil.get_published_articles(account_type, 1, 1, False)
            draft_result = WechatArticlesUtil.get_draft_articles(account_type, 1, 1, False)

            stats = {
                "account_type": account_type,
                "published_count": 0,
                "draft_count": 0,
                "total_count": 0
            }

            if published_result.get("success"):
                stats["published_count"] = published_result["data"]["pagination"]["total"]

            if draft_result.get("success"):
                stats["draft_count"] = draft_result["data"]["pagination"]["total"]

            stats["total_count"] = stats["published_count"] + stats["draft_count"]

            self.success_response(stats, message="获取统计信息成功")

        except Exception as e:
            logger.error("获取文章统计信息失败", error=str(e))
            raise HTTPError(500, f"获取统计信息失败: {str(e)}")