"""
文章API处理器
"""
from typing import Dict, Any, Optional, List
import structlog
from tornado.web import HTT<PERSON><PERSON>rror

from app.api.base import BaseHandler
from app.schemas.article.article import (
    ArticleCreate, 
    ArticleUpdate, 
    ArticleResponse, 
    ArticleListResponse,
    ArticleLikeRequest,
    ArticleShareRequest
)
from app.schemas.article.comment import CommentSearchRequest
from app.services.article.article_service import ArticleService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)


class ArticleHandler(BaseHandler):
    """文章处理器"""

    @inject
    def initialize(
        self,
        article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器
        
        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()


    # @require_auth(required=True)
    async def post(self) -> None:
        """创建文章"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            
            # 从请求体获取文章数据
            request_data = self.json_body
            article_data = ArticleCreate(**request_data)
            
            # 调用服务创建文章
            article = await self.article_service.create_article(article_data, user_id)
            
            # 返回结果
            self.success_response(article.dict())
            
        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message=f"请求数据无效: {str(e)}")
        except Exception as e:
            logger.error("创建文章时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")

    async def get(self) -> None:
        """获取文章详情或列表"""

        article_id = self.get_argument("article_id", None)
        try:
            if article_id:
                # 获取单个文章详情
                auth_header = self.request.headers.get('Authorization')
                user_id = get_current_user_id(auth_header) if auth_header else None
                article = await self.article_service.get_article_by_id(article_id, user_id)
                
                if not article:
                    self.write_error(500, error_message="文章不存在")
                    return

                # 增加阅读量
                await self.article_service.increment_read_count_and_record_history(article_id,user_id)

                self.success_response(article.dict())
            else:
                # 获取文章列表
                page_str = self.get_argument("page", "1")
                page_size_str = self.get_argument("page_size", "10")
                status = self.get_argument('status', None)
                raw_tags = self.get_arguments('tags')
                tags = None
                if raw_tags and raw_tags != ['']:  # 检查是否为空列表或只包含空字符串
                    tags = [tag for tag in raw_tags if tag.strip()]  # 过滤空字符串
                    if not tags:  # 如果过滤后为空，设置为 None
                        tags = None

                # ''
                status = self.get_argument('status', None)
                status = None if status == '' else status

                search = self.get_argument('search', None)
                search = None if search == '' else search

                is_public_str = self.get_argument('is_public', None)
                is_public_str = None if is_public_str == '' else is_public_str

                need_recommend = self.get_argument("need_recommend", "true").lower() == "true"
                recommend_tags_str = self.get_argument("recommend_tags", None)
                recommend_tags = None
                if recommend_tags_str:
                    # 使用逗号分隔的字符串格式，例如："python,java,csharp"
                    try:
                        # 按逗号分割，并去除每个标签的前后空格
                        recommend_tags = [tag.strip() for tag in recommend_tags_str.split(",") if tag.strip()]
                        # 如果分割后没有有效标签，设置为None
                        if not recommend_tags:
                            recommend_tags = None
                    except Exception as e:
                        logger.warning(f"解析推荐标签失败: {str(e)}, recommend_tags_str: {recommend_tags_str}")
                        recommend_tags = None

                try:
                    page = int(page_str) if page_str.strip() else 1
                except ValueError:
                    page = 1

                try:
                    size = int(page_size_str) if page_size_str.strip() else 30
                except ValueError:
                    size = 30

                # 获取当前用户ID（如果已认证）
                auth_header = self.request.headers.get('Authorization')
                user_id = get_current_user_id(auth_header) if auth_header else None



                articles = await self.article_service.get_articles(
                    page=page,
                    size=size,
                    status=status,
                    tags=tags,
                    search=search,
                    is_public=is_public_str,
                    user_id=user_id,
                    need_recommend=need_recommend,
                    recommend_tags=recommend_tags
                )
                
                self.success_response(articles.dict())
                
        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(500, error_message=f"请求参数无效: {str(e)}")
        except Exception as e:
            logger.error("获取文章时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")

    # @require_auth(required=True)
    async def put(self) -> None:
        """更新文章"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            
            # 从请求体获取更新数据
            request_data = self.json_body
            article_data = ArticleUpdate(**request_data)
            
            # 调用服务更新文章
            article = await self.article_service.update_article(article_data.article_id, article_data, user_id)
            
            if not article:
                self.write_error(500, error_message="文章不存在")
                return
            
            self.success_response(article.dict())
            
        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message=f"请求数据无效: {str(e)}")
        except Exception as e:
            logger.error("更新文章时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")

    # @require_auth(required=True)
    async def delete(self, article_id: str) -> None:
        """删除文章"""
        try:
            # 调用服务删除文章
            success = await self.article_service.delete_article(article_id)
            if not success:
                self.write_error(500, error_message="文章不存在")
                return
            
            self.success_response({"message": "文章删除成功"})
            
        except Exception as e:
            logger.error("删除文章时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class ArticleLikeHandler(BaseHandler):
    """文章点赞处理器"""

    @inject
    def initialize(
        self,
        article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器
        
        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    @require_auth(required=True)
    async def post(self, article_id: str) -> None:
        """点赞，收藏或取消点赞文章"""
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            
            # 从请求体获取操作类型
            request_data = self.json_body
            like_data = ArticleLikeRequest(**request_data)
            
            # 调用服务执行点赞操作
            success = await self.article_service.like_article(article_id, user_id, like_data.action)
            
            if not success:
                self.write_error(500, error_message="文章不存在")
                return
            
            action_text = "点赞" if like_data.action == "like" else "取消点赞"
            self.success_response({"message": f"文章{action_text}成功"})
            
        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(400, error_message=f"请求数据无效: {str(e)}")
        except Exception as e:
            logger.error("点赞操作时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class ArticleShareHandler(BaseHandler):
    """文章分享处理器"""

    @inject
    def initialize(
        self,
        article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器
        
        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    async def post(self, article_id: str) -> None:
        """分享文章"""
        try:
            # 从请求体获取分享类型
            request_data = self.json_body
            share_data = ArticleShareRequest(**request_data)
            
            # 调用服务分享文章
            share_link = await self.article_service.share_article(article_id, share_data.share_type)
            
            if not share_link:
                self.write_error(500, error_message="文章不存在")
                return
            
            self.success_response({
                "message": "分享成功",
                "share_link": share_link,
                "share_type": share_data.share_type
            })
            
        except ValueError as e:
            logger.error("请求数据无效", error=str(e))
            self.write_error(500, error_message=f"请求数据无效: {str(e)}")
        except Exception as e:
            logger.error("分享文章时发生错误", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")



class ArticleCommentSearchHandler(BaseHandler):
    """评论搜索处理器"""

    @inject
    def initialize(
            self,
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    async def post(self) -> None:
        """搜索评论"""
        try:
            # 从请求体获取搜索参数
            request_data = self.json_body
            if not request_data:
                logger.error("请求体为空")
                self.write_error(400, error_message="请求体不能为空")
                return

            try:
                search_request = CommentSearchRequest(**request_data)
            except ValueError as e:
                logger.error("请求数据无效", error=str(e), request_data=request_data)
                self.write_error(400, error_message=f"请求数据无效: {str(e)}")
                return

            # 调用服务搜索评论
            search_result = await self.article_service.search_comments(search_request)
            # 返回结果
            self.success_response(search_result.dict())

        except Exception as e:
            logger.error("搜索评论失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")

    async def get(self) -> None:
        """GET方式搜索评论（用于简单查询）"""
        try:
            # 获取查询参数
            project_id = self.get_argument("project_id")
            query = self.get_argument("query", None)
            user_name = self.get_argument("user_name", None)
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            sort_by = self.get_argument("sort_by", "created_at")
            sort_order = self.get_argument("sort_order", "desc")

            # 构造搜索请求
            search_request = CommentSearchRequest(
                project_id=project_id,
                query=query,
                user_name=user_name,
                page=page,
                page_size=page_size,
                sort_by=sort_by,
                sort_order=sort_order
            )

            # 调用服务搜索评论
            # search_result = await self.article_service.search_comments(search_request)
            search_result = await self.article_service.simple_search_comments(project_id, query, page, page_size)

            # 返回结果
            self.success_response(search_result)

        except Exception as e:
            logger.error("搜索评论失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")
