#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文章生成API路由配置
"""

from .interactive_planning import InteractivePlanningWebSocketHandler
from .article_generation import (
    ArticleGenerateHandler,
    ArticlePreviewHandler,
    ArticleValidateHandler,
    ArticleStatusHandler,
    ArticleTemplatesHandler
)
from .data_sources import (
    DataSourceListHandler,
    DataSourceStatusHandler,
    DataSourceQueryHandler
)


def get_intelligent_article_routes():
    """
    获取智能文章生成相关的路由配置
    
    Returns:
        List[tuple]: 路由配置列表
    """
    return [
        # WebSocket路由 - 交互式规划
        (r"/api/v1/article/planning/ws", InteractivePlanningWebSocketHandler),

        # HTTP路由 - 文章生成
        (r"/api/v1/article/generation/generate", ArticleGenerateHandler),
        (r"/api/v1/article/generation/preview", ArticlePreviewHandler),
        (r"/api/v1/article/generation/validate", ArticleValidateHandler),
        (r"/api/v1/article/generation/status", ArticleStatusHandler),
        (r"/api/v1/article/generation/templates", ArticleTemplatesHandler),

        # HTTP路由 - 数据源管理
        (r"/api/v1/article/data-sources/list", DataSourceListHandler),
        (r"/api/v1/article/data-sources/status", DataSourceStatusHandler),
        (r"/api/v1/article/data-sources/query", DataSourceQueryHandler),
    ]



