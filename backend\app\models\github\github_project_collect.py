#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/28 15:39
# @File    : github_project_collect.py
# @Description: 
"""
"""
用户收藏项目数据库模型
"""
from sqlalchemy import Column, String, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship

from app.models.model_base import ModelBase


class UserProjectCollectModel(ModelBase):
    """用户收藏项目关系表"""
    __tablename__ = "user_project_collects"

    # 用户ID，外键关联到users表
    user_id = Column(String(64), ForeignKey('users.id'), nullable=False, comment='用户ID')

    # 项目ID，外键关联到github_projects表
    project_id = Column(String(64), ForeignKey('github_projects.id'), nullable=False, comment='项目ID')

    # 建立唯一约束，防止用户重复收藏同一个项目
    __table_args__ = (
        UniqueConstraint('user_id', 'project_id', name='uq_user_project_collect'),
    )

    # 关系定义
    user = relationship("UserModel", lazy="select")
    project = relationship("GitHubProjectModel", lazy="select")

    def __repr__(self):
        return f"<UserProjectCollect user_id={self.user_id} project_id={self.project_id}>"