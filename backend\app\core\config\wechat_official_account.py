#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/7 17:34
# @File    : wechat_official_account.py
# @Description: 
"""
"""
OAuth相关配置
"""
from typing import List
from pydantic import Field, field_validator
from .base import BaseAppConfig


class WechatOfficialAccountSettings(BaseAppConfig):
    """OAuth提供商配置基类"""

    WEIXIN_OFFICIAL_TOKEN: str = Field(
        default="shiyu666",
        description="OAuth客户端ID"
    )
    WEIXIN_OFFICIAL_ENCODING_AES_KEY: str = Field(
        default="",
        description="微信 aes key"
    )
    WEIXIN_OFFICIAL_APP_ID: str = Field(
        default="wx68e5bb26923a081e",
        description="服务号id"
    )
    WEIXIN_OFFICIAL_APP_SECRET: str = Field(
        default="7107786e03a18ae2b1b1d84659c57946",
        description="服务号密钥"
    )
    WEIXIN_MP_ID: str = Field(
        default="wx71395fe23cb25b3e",
        description="服务号id"
    )
    WEIXIN_MP_SECRET: str = Field(
        default="1c58eba685baa0b54e99f6fa61799ccf",
        description="服务号密钥"
    )
