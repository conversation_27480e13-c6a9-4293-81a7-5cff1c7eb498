import os
import re
import json
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage


def split_markdown_with_llm(markdown_text):
    """使用LangChain和大模型API分割Markdown文档"""

    # 初始化LangChain的ChatOpenAI客户端
    llm = ChatOpenAI(
        temperature=0.0,
        model="deepseek-reasoner",
        base_url="https://api.deepseek.com/v1",
        max_tokens=8192,
        api_key="sk-b31c5a7c8d054f2db7d038a321d4920d"
    )

    prompt = f"""
    请将以下Markdown文档分割成多个独立的Markdown文档。
    为每个分割出的文档提供一个合适的标题和文件名。

    要求:
    1. 返回纯JSON格式，不要包含代码块标记
    2. 使用如下格式的JSON数组：[{{"filename": "文件名.md", "content": "文件内容"}}, ...]
    3. 每个文件应该有一个逻辑上独立的内容部分

    原始Markdown文档:
    ```
    {markdown_text}
    ```
    """

    # 使用LangChain调用大模型
    messages = [HumanMessage(content=prompt)]
    response = llm.invoke(messages)

    # 处理响应，提取分割后的文档
    result = response.content

    return result


def clean_json_response(json_result):
    """清理模型返回的JSON字符串，移除代码块标记"""

    # 尝试提取代码块中的内容
    code_block_match = re.search(r'```(?:json)?\s*([\s\S]*?)```', json_result)
    if code_block_match:
        return code_block_match.group(1).strip()

    # 如果没有代码块标记，则返回原始结果
    return json_result.strip()


def save_split_documents(json_result, output_dir="."):
    """
    解析JSON结果并保存分割后的文档

    参数:
    - json_result: 从LLM返回的JSON字符串
    - output_dir: 输出目录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 清理JSON字符串
    cleaned_json = clean_json_response(json_result)
    print("清理后的JSON:", cleaned_json[:100] + "..." if len(cleaned_json) > 100 else cleaned_json)

    try:
        # 解析JSON
        documents = json.loads(cleaned_json)

        # 处理列表格式 [{"filename": "...", "content": "..."}, ...]
        if isinstance(documents, list):
            for doc_item in documents:
                if "filename" in doc_item and "content" in doc_item:
                    # 获取文件名和内容
                    doc_name = doc_item["filename"]
                    content = doc_item["content"]

                    # 确保文件名是合法的
                    safe_name = re.sub(r'[^\w\s.-]', '_', doc_name)
                    if not safe_name.endswith('.md'):
                        safe_name += '.md'

                    file_path = os.path.join(output_dir, safe_name)

                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)

                    print(f"已保存文档: {file_path}")
                else:
                    print(f"警告: 文档项缺少必要的字段: {doc_item}")

        # 处理字典格式 {"filename.md": "content", ...}
        elif isinstance(documents, dict):
            for doc_name, content in documents.items():
                # 确保文件名是合法的
                safe_name = re.sub(r'[^\w\s.-]', '_', doc_name)
                if not safe_name.endswith('.md'):
                    safe_name += '.md'

                file_path = os.path.join(output_dir, safe_name)

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                print(f"已保存文档: {file_path}")

        else:
            print("错误: 不支持的JSON格式，既不是字典也不是列表")
            print(f"解析后的数据类型: {type(documents)}")
            print(f"数据内容: {documents}")
            return False

        return True

    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        print("原始结果:", json_result)
        print("清理后:", cleaned_json)
        return False


# 使用示例
if __name__ == "__main__":
    # 从文件读取Markdown内容
    with open('test.md', 'r', encoding='utf-8') as f:
        content = f.read()

    # 使用LLM分割文档
    json_result = split_markdown_with_llm(content)
    print("LLM返回结果:", json_result)

    # 保存分割后的文档
    save_split_documents(json_result, output_dir="split_documents")