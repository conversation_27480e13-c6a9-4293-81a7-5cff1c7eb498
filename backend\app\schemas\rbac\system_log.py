#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/14 13:10
# @File    : system_log.py
# @Description: 
"""
#!/usr/bin/env python
# -*- coding: utf-8 -*-


from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Optional


class SystemLogCreate(BaseModel):
    """创建系统日志请求模型"""
    message: str = Field(..., description="系统消息内容")
    user_id: str = Field(..., description="用户ID")
    message_type: Optional[str] = Field("system", description="消息类型")
    project_id: Optional[str] = Field(None, description="关联项目ID")


class SystemLogUpdate(BaseModel):
    """更新系统日志请求模型"""
    is_read: bool = Field(..., description="是否已读")


class SystemLog(BaseModel):
    """系统日志响应模型"""
    id: str = Field(..., description="日志ID")
    message: str = Field(..., description="系统消息内容")
    created_by: str = Field(..., description="用户ID")
    is_read: bool = Field(..., description="是否已读")
    message_type: Optional[str] = Field(None, description="消息类型")
    project_id: Optional[str] = Field(None, description="关联项目ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        """配置"""
        from_attributes = True


class SystemLogList(BaseModel):
    """系统日志列表响应模型"""
    logs: List[SystemLog] = Field(..., description="日志列表")
    total: int = Field(..., description="总数")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    unread_count: int = Field(..., description="未读消息数量")


class SystemLogReadRequest(BaseModel):
    """标记已读请求模型"""
    log_ids: List[str] = Field(..., description="要标记为已读的日志ID列表")