"""
GitHub项目搜索API

提供统一的项目和卡片搜索功能，支持多种过滤条件和排序选项
"""
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone
import aiohttp
from urllib.parse import urlparse

from app.api.base import BaseHandler
from app.services.elasticsearch.project_indexer import ProjectIndexer
from app.services.github import GitHubProjectService
from app.services.github.pending_repository_service import PendingRepositoryService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
import structlog

from app.utils.status_enum import ProjectStatusEnum

logger = structlog.get_logger(__name__)


class ProjectSearchHandler(BaseHandler):
    """项目搜索处理器"""

    @inject
    def initialize(
        self,
        project_indexer: ProjectIndexer = Provide[Container.project_indexer]
    ):
        """初始化处理器

        Args:
            project_indexer: 项目 Elasticsearch搜索服务
        """
        self.indexer = project_indexer
        super().initialize()

    async def get(self):
        """统一搜索项目数据，同时支持项目和卡片搜索"""
        try:
            # 获取基本参数
            query = self.get_argument("query", "")
            size = int(self.get_argument("size", "10"))
            page = int(self.get_argument("page", "1"))
            sort_by = self.get_argument("sort_by", None)
            sort_order = self.get_argument("sort_order", "desc")
            highlight = self.get_argument("highlight", "true").lower() == "true"

            # 过滤参数处理
            filters = {}

            # 项目相关参数
            tags_param = self.get_argument("tags", None)
            if tags_param:
                filters["tags"] = tags_param.split(",")

            status = self.get_argument("status", None)
            if status:
                filters["status"] = status

            # 处理日期范围参数
            created_after = self.get_argument("created_after", None)
            created_before = self.get_argument("created_before", None)

            if created_after or created_before:
                date_range = {}
                if created_after:
                    try:
                        # 尝试解析日期，确保格式正确
                        datetime.fromisoformat(created_after.replace('Z', '+00:00'))
                        date_range["gte"] = created_after
                    except ValueError:
                        logger.warning(f"无效的created_after日期格式: {created_after}")

                if created_before:
                    try:
                        datetime.fromisoformat(created_before.replace('Z', '+00:00'))
                        date_range["lte"] = created_before
                    except ValueError:
                        logger.warning(f"无效的created_before日期格式: {created_before}")

                if date_range:
                    filters["created_at"] = date_range

            # 记录日志
            logger.info(
                "执行项目搜索",
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                size=size,
            )

            # 执行搜索
            search_result = await self.indexer.search_projects(
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=size,
                highlight=highlight
            )

            # 检查搜索结果是否包含错误
            if "error" in search_result:
                error_message = search_result.get("error", "未知搜索错误")
                logger.error("搜索失败", error=error_message)
                return self.write_error(500, error_message=f"搜索遇到错误: {error_message}")

            # 返回搜索结果
            return self.success_response(search_result)

        except ValueError as e:
            # 处理参数类型转换错误
            logger.error("参数格式错误", error=str(e))
            return self.write_error(400, error_message=f"参数格式错误: {str(e)}")
        except Exception as e:
            # 处理其他未知错误
            logger.error("搜索失败", error=str(e), exc_info=True)
            return self.write_error(500, error_message=f"搜索遇到错误: {str(e)}")

    async def post(self):
        """高级搜索接口，支持复杂过滤条件"""
        try:
            # 解析请求体
            request_data = self.json_body
            if not request_data:
                return self.write_error(400, error_message="请求体不能为空")

            # 获取搜索参数
            query = request_data.get("query", "")
            filters = request_data.get("filters", {})
            sort_by = request_data.get("sort_by")
            sort_order = request_data.get("sort_order", "desc")
            page = request_data.get("page", 1)
            size = request_data.get("size", 10)
            highlight = request_data.get("highlight", True)

            # 执行搜索
            search_result = await self.indexer.search_projects(
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=size,
                highlight=highlight
            )

            # 检查搜索结果是否包含错误
            if "error" in search_result:
                error_message = search_result.get("error", "未知搜索错误")
                logger.error("高级搜索失败", error=error_message)
                return self.write_error(500, error_message=f"高级搜索遇到错误: {error_message}")

            # 返回搜索结果
            return self.success_response(search_result)

        except Exception as e:
            logger.error("高级搜索失败", error=str(e), exc_info=True)
            return self.write_error(500, error_message=f"高级搜索遇到错误: {str(e)}")


class RepoUrlValidationHandler(BaseHandler):
    """仓库URL验证处理器
    
    验证输入的URL是否为有效的开源项目仓库地址，并检查该仓库是否已经在搜索引擎中存在
    """

    @inject
    def initialize(
        self,
        project_indexer: ProjectIndexer = Provide[Container.project_indexer],
        pending_repository_service: PendingRepositoryService = Provide[Container.pending_repository_service],
        github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            project_indexer: 项目 Elasticsearch搜索服务
            pending_repository_service: 待处理仓库服务
        """
        self.indexer = project_indexer
        self.pending_service = pending_repository_service
        self.github_project_service = github_project_service
        super().initialize()
        
    async def post(self):
        """验证仓库URL
        
        请求格式:
        {
            "url": "https://github.com/username/repo"
        }
        
        响应格式:
        {
            "valid": true|false,        # URL是否为有效的仓库地址
            "exists": true|false,       # 仓库是否已存在于搜索系统中
            "project": {               # 如果存在，则包含项目信息
                "id": "项目ID",
                "name": "项目名称",
                "description": "项目描述",
                ...
            },
            "pending_created": true|false,  # 是否创建了待处理记录
            "pending_id": "待处理记录ID"     # 待处理记录的ID（如果创建成功）
        }
        """
        try:
            # 获取请求体
            data = self.json_body
            
            # 验证必须参数
            if not data or "url" not in data:
                return self.write_error(400, error_message="请提供有效的仓库URL")
                
            url = data["url"].strip()
            
            # 验证URL格式和仓库是否真实存在
            is_valid = await self._validate_repo_url(url)
            
            # 准备响应数据
            response = {
                "valid": is_valid,
                "exists": False,
                "project": None,
                "pending_created": False,
                "pending_id": None
            }

            # 如果URL格式有效，则查询是否已存在于索引中
            if is_valid:
                existing_project_search = await self._check_repo_exists(url)
                existing_project_database = await self._check_repo_exists_in_database(url)
                if existing_project_database:
                    response["exists"] = True
                    response["project"] = existing_project_search
                else:
                    # 仓库验证有效但不存在于索引中，创建待处理记录
                    pending_record = await self._create_pending_repository(url)
                    if pending_record:
                        response["pending_created"] = True
                        response["pending_id"] = pending_record.id
                        logger.info("创建待处理仓库记录成功",
                                   url=url,
                                   pending_id=pending_record.id)

            self.success_response(response)
            
        except Exception as e:
            logger.error(f"处理仓库URL验证时出错: {str(e)}")
            return self.write_error(500, error_message=f"验证仓库URL时发生错误: {str(e)}")

    async def _check_repo_exists_in_database(self, url: str) -> Optional[Dict[str, Any]]:
        """检查仓库是否已存在于数据库中而且前端能够显示

        Args:
            url: 仓库URL

        Returns:
            Optional[Dict[str, Any]]: 符合条件则返回项目信息，否则返回None
        """
        try:
            # 生成多种可能的URL格式进行查询
            possible_urls = self._generate_possible_urls(url)

            # 在数据库中查询每个可能的URL
            for possible_url in possible_urls:
                project = await self.github_project_service.get_project_by_url(possible_url)
                if project and (project.status == ProjectStatusEnum.PUBLISHED.value):
                    # 转换为字典格式返回
                    return {
                        "id": project.id,
                        "name": project.name,
                        "repository_url": project.repository_url,
                        "description_recommend": project.description_recommend,
                        "description_project": project.description_project,
                        "tags": project.tags,
                        "status": project.status,
                        "project_phase": project.project_phase,
                        "stars": project.stars,
                        "background_color": project.background_color,
                        "button_color": project.button_color,
                        "image_url": project.image_url,
                        "icon_url": project.icon_url,
                        "created_at": project.created_at.isoformat() if project.created_at else None,
                        "updated_at": project.updated_at.isoformat() if project.updated_at else None
                    }

            return None

        except Exception as e:
            logger.error(f"检查仓库在数据库中是否存在时出错: {str(e)}")
            return None

    async def _validate_repo_url(self, url: str) -> bool:
        """验证URL是否为有效的仓库地址
        
        Args:
            url: 待验证的URL
            
        Returns:
            bool: 是否为有效的仓库地址
        """
        try:
            # 解析URL
            parsed_url = urlparse(url)
            
            # 验证URL格式
            if not parsed_url.scheme or not parsed_url.netloc:
                return False
                
            # 验证是否为常见代码托管平台
            valid_hosts = ["github.com", "gitlab.com", "bitbucket.org", "gitee.com", "coding.net"]
            if parsed_url.netloc not in valid_hosts and not any(host in parsed_url.netloc for host in valid_hosts):
                return False
                
            # GitHub格式: https://github.com/username/repo
            # GitLab格式: https://gitlab.com/username/repo
            # Bitbucket格式: https://bitbucket.org/username/repo
            # Gitee格式: https://gitee.com/username/repo
            
            # 验证路径格式，应该至少包含用户名和仓库名
            path_parts = [p for p in parsed_url.path.split("/") if p]
            if len(path_parts) < 2:
                return False
            
            # 验证仓库是否真实存在
            username, repo_name = path_parts[0], path_parts[1]
            exists, _ = await self._check_repo_existence(parsed_url.netloc, username, repo_name)
            if not exists:
                logger.info(f"仓库不存在: {url}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"验证仓库URL时出错: {str(e)}")
            return False
    
    async def _check_repo_exists(self, url: str) -> Optional[Dict[str, Any]]:
        """检查仓库是否已存在于索引中
        
        Args:
            url: 仓库URL
            
        Returns:
            Optional[Dict[str, Any]]: 存在则返回项目信息，否则返回None
        """
        try:
            # 生成多种可能的URL格式进行查询
            possible_urls = self._generate_possible_urls(url)
            
            # 使用terms查询多个可能的URL形式
            search_result = await self.indexer.search_projects(
                filters={"repository_url": possible_urls},
                page=1,
                page_size=1,
                highlight=False
            )
            
            if search_result.get("total", 0) > 0 and search_result.get("projects"):
                # 返回匹配的项目基本信息
                project = search_result["projects"][0]
                return project
            
            return None
            
        except Exception as e:
            logger.error(f"检查仓库是否存在时出错: {str(e)}")
            return None
    
    def _generate_possible_urls(self, url: str) -> List[str]:
        """生成可能的仓库URL匹配形式
        
        生成多种可能的URL形式进行查询，以提高匹配成功率
        
        Args:
            url: 原始URL
            
        Returns:
            List[str]: 可能的URL形式列表
        """
        possible_urls = []
        
        try:
            # 保存原始版本（仅去除空格）
            original_url = url.strip()
            possible_urls.append(original_url)
            
            # 转为小写版本进行后续处理
            url = original_url.lower()
            
            # 解析原始URL和小写URL
            original_parsed_url = urlparse(original_url)
            parsed_url = urlparse(url)
            
            # 提取域名和路径
            domain = parsed_url.netloc
            path = parsed_url.path
            
            # 移除结尾斜杠
            path = path.rstrip("/")
            
            # 移除.git后缀
            if path.endswith(".git"):
                path = path[:-4]
            
            # 版本1: 小写URL (原始URL已在前面添加)
            
            # 版本2: 带协议的完整域名+路径
            possible_urls.append(f"{parsed_url.scheme}://{domain}{path}")
            
            # 版本3: 不带协议的域名+路径
            possible_urls.append(f"{domain}{path}")
            
            # 版本4: 去掉www前缀
            if domain.startswith("www."):
                domain_no_www = domain[4:]
                possible_urls.append(f"{parsed_url.scheme}://{domain_no_www}{path}")
                possible_urls.append(f"{domain_no_www}{path}")
                
            # 版本5: 添加www前缀
            if not domain.startswith("www."):
                domain_with_www = f"www.{domain}"
                possible_urls.append(f"{parsed_url.scheme}://{domain_with_www}{path}")
                possible_urls.append(f"{domain_with_www}{path}")
                
            # 如果是GitHub，使用特殊格式
            if "github.com" in domain:
                # 小写路径的处理
                path_parts = [p for p in path.split("/") if p]
                if len(path_parts) >= 2:
                    username = path_parts[0]
                    repo_name = path_parts[1]
                    possible_urls.append(f"github.com/{username}/{repo_name}")
                    possible_urls.append(f"{parsed_url.scheme}://github.com/{username}/{repo_name}")
                
                # 原始路径的处理（保留大小写）
                original_path = original_parsed_url.path
                original_path = original_path.rstrip("/")
                if original_path.lower().endswith(".git"):
                    original_path = original_path[:-4]
                    
                original_path_parts = [p for p in original_path.split("/") if p]
                if len(original_path_parts) >= 2:
                    original_username = original_path_parts[0]
                    original_repo_name = original_path_parts[1]
                    possible_urls.append(f"github.com/{original_username}/{original_repo_name}")
                    possible_urls.append(f"{original_parsed_url.scheme}://github.com/{original_username}/{original_repo_name}")
                    
                    # 添加原始域名的大小写结合
                    original_domain = original_parsed_url.netloc
                    possible_urls.append(f"{original_domain}{original_path}")
                    possible_urls.append(f"{original_parsed_url.scheme}://{original_domain}{original_path}")
                    
                    # 添加不同对URL组件的大小写组合
                    possible_urls.append(f"{original_parsed_url.scheme}://{original_domain}/{original_username}/{original_repo_name}")
                    possible_urls.append(f"{original_domain}/{original_username}/{original_repo_name}")
                    
            # 去重并返回唯一的URL列表
            return list(set(possible_urls))
            
        except Exception as e:
            logger.error(f"生成可能的URL匹配形式时出错: {str(e)}")
            return [url]  # 如果出错则返回原始URL
            
    async def _check_repo_existence(self, host: str, username: str, repo_name: str) -> Tuple[bool, Dict[str, Any]]:
        """检查仓库是否真实存在
        
        Args:
            host: 代码托管平台域名
            username: 用户名/组织名
            repo_name: 仓库名
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否存在, 附加信息)
        """
        try:
            timeout = aiohttp.ClientTimeout(total=15)  # 设置超时时间为15秒
            
            # 根据不同平台使用不同的API
            if "github.com" in host:
                # GitHub API
                api_url = f"https://api.github.com/repos/{username}/{repo_name}"
                headers = {"Accept": "application/vnd.github.v3+json"}
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(api_url, headers=headers) as response:
                        if response.status == 200:
                            data = await response.json()
                            return True, data
                        elif response.status == 404:
                            return False, {}
                        else:
                            # 如果API请求受限，尝试直接访问HTML页面
                            html_url = f"https://github.com/{username}/{repo_name}"
                            async with session.get(html_url) as html_response:
                                return html_response.status == 200, {}
                        
            elif "gitlab.com" in host:
                # GitLab API
                api_url = f"https://gitlab.com/api/v4/projects/{username}%2F{repo_name}"
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(api_url) as response:
                        if response.status == 200:
                            data = await response.json()
                            return True, data
                        elif response.status == 404:
                            return False, {}
                        else:
                            # 尝试直接访问HTML页面
                            html_url = f"https://gitlab.com/{username}/{repo_name}"
                            async with session.get(html_url) as html_response:
                                return html_response.status == 200, {}
                
            elif "gitee.com" in host:
                # Gitee API
                api_url = f"https://gitee.com/api/v5/repos/{username}/{repo_name}"
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(api_url) as response:
                        if response.status == 200:
                            data = await response.json()
                            return True, data
                        elif response.status == 404:
                            return False, {}
                        else:
                            # 尝试直接访问HTML页面
                            html_url = f"https://gitee.com/{username}/{repo_name}"
                            async with session.get(html_url) as html_response:
                                return html_response.status == 200, {}
                                
            elif "bitbucket.org" in host:
                # Bitbucket API
                api_url = f"https://api.bitbucket.org/2.0/repositories/{username}/{repo_name}"
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(api_url) as response:
                        if response.status == 200:
                            data = await response.json()
                            return True, data
                        elif response.status == 404:
                            return False, {}
                        else:
                            # 尝试直接访问HTML页面
                            html_url = f"https://bitbucket.org/{username}/{repo_name}"
                            async with session.get(html_url) as html_response:
                                return html_response.status == 200, {}
            
            else:
                # 对于其他不支持的平台，尝试直接访问网页
                url = f"https://{host}/{username}/{repo_name}"
                
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    async with session.get(url) as response:
                        return response.status == 200, {}
                        
        except Exception as e:
            logger.warning(f"检查仓库存在性时出错: {url}, 错误: {str(e)}")
            # 如果发生错误，我们假设仓库存在，让后续验证过程继续
            # 这种处理方式确保网络问题不会阻止合法仓库验证
            return True, {}

    async def _create_pending_repository(self, url: str):
        """创建待处理仓库记录

        Args:
            url: 仓库URL

        Returns:
            Optional[GitHubPendingRepositoryModel]: 创建的待处理记录，失败时返回None
        """
        try:
            # 获取请求上下文信息
            submitted_by_ip = self.get_client_ip()
            submitted_by_user_agent = self.request.headers.get("User-Agent")

            # 构建提交元数据
            submission_metadata = {
                "request_time": datetime.now(timezone.utc).isoformat(),
                "request_method": self.request.method,
                "request_uri": self.request.uri,
                "referer": self.request.headers.get("Referer"),
                "accept_language": self.request.headers.get("Accept-Language"),
                "validation_source": "api_url_validation"
            }

            # 获取提交者用户名
            if self.current_user:
                submitted_by_user = self.current_user.username
            else:
                submitted_by_user = None

            # 使用服务创建待处理记录
            pending_record = await self.pending_service.create_from_url_validation(
                repository_url=url,
                submitted_by_ip=submitted_by_ip,
                submitted_by_user=submitted_by_user,
                submitted_by_user_agent=submitted_by_user_agent,
                submission_metadata=submission_metadata
            )

            return pending_record

        except Exception as e:
            logger.error("创建待处理仓库记录失败", url=url, error=str(e))
            return None

    def get_client_ip(self) -> Optional[str]:
        """获取客户端IP地址

        Returns:
            Optional[str]: 客户端IP地址
        """
        try:
            # 尝试从X-Forwarded-For头获取真实IP
            forwarded_for = self.request.headers.get("X-Forwarded-For")
            if forwarded_for:
                # X-Forwarded-For可能包含多个IP，取第一个
                return forwarded_for.split(",")[0].strip()

            # 尝试从X-Real-IP头获取
            real_ip = self.request.headers.get("X-Real-IP")
            if real_ip:
                return real_ip.strip()

            # 使用远程IP
            return self.request.remote_ip

        except Exception as e:
            logger.warning("获取客户端IP失败", error=str(e))
            return None