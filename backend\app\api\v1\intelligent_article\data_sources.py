#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文章生成 - 数据源管理API
"""

import logging
from typing import Dict, Any, List, Optional
from tornado.web import HTTPError
from pydantic import BaseModel, Field
from dependency_injector.wiring import Provide, inject

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.reasoning.intelligent_article_generator.data_sources.data_source_manager import DataSourceManager
from app.services.reasoning.intelligent_article_generator.models.data_source_models import DataSourceType

logger = logging.getLogger(__name__)


class DataSourceQueryRequest(BaseModel):
    """数据源查询请求模型"""
    query: str = Field(description="查询关键词")
    source_types: Optional[List[str]] = Field(default=None, description="数据源类型列表")
    limit: Optional[int] = Field(default=10, description="返回结果数量限制")
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外过滤条件")


class DataSourceListHandler(BaseHandler):
    """数据源列表处理器"""

    @inject
    def initialize(
        self,
        data_source_manager: DataSourceManager = Provide[Container.data_source_manager]
    ):
        """初始化处理器"""
        super().initialize()
        self.data_source_manager: DataSourceManager = data_source_manager
        
    async def get(self):
        """列出所有可用的数据源"""
        try:
            # 使用 data_sources 属性获取数据源字典
            data_sources = self.data_source_manager.data_sources

            source_info = []
            for source_type, source_instance in data_sources.items():
                info = {
                    "name": source_instance.data_source.name,
                    "type": source_instance.data_source.source_type.value,
                    "description": source_instance.data_source.description,
                    "is_available": source_instance.is_healthy(),
                    "rate_limit": source_instance.config.rate_limit
                }
                source_info.append(info)

            self.success_response({
                "data_sources": source_info,
                "total_count": len(source_info)
            }, "数据源列表获取成功")

        except Exception as e:
            logger.error(f"获取数据源列表失败: {str(e)}")
            raise HTTPError(500, f"获取数据源列表失败: {str(e)}")


class DataSourceStatusHandler(BaseHandler):
    """数据源状态处理器"""

    @inject
    def initialize(
        self,
        data_source_manager: DataSourceManager = Provide[Container.data_source_manager]
    ):
        """初始化处理器"""
        super().initialize()
        self.data_source_manager: DataSourceManager = data_source_manager
        
    async def get(self):
        """获取数据源状态"""
        try:
            source_name = self.get_argument("source", None)
            
            if source_name:
                # 获取特定数据源状态
                # 查找匹配的数据源类型
                target_source_type = None
                for source_type in self.data_source_manager.data_sources.keys():
                    if source_type.value == source_name:
                        target_source_type = source_type
                        break

                if not target_source_type or target_source_type not in self.data_source_manager.data_sources:
                    raise HTTPError(404, f"数据源 '{source_name}' 不存在")

                source = self.data_source_manager.data_sources[target_source_type]
                source_data = source.get_status()

                status = {
                    "name": source_name,
                    "is_available": source.is_healthy(),
                    "status": source_data.status.value,
                    "total_requests": source_data.total_requests,
                    "success_rate": source_data.get_success_rate(),
                    "last_success_at": source_data.last_success_at,
                    "last_error": source_data.last_error,
                    "total_data_collected": source_data.total_data_collected
                }

                self.success_response(status, f"数据源 '{source_name}' 状态获取成功")
            else:
                # 获取所有数据源状态
                status_dict = self.data_source_manager.get_source_status()
                status_list = []

                for source_type, status_info in status_dict.items():
                    status_list.append({
                        "name": source_type.value,
                        **status_info
                    })

                self.success_response({
                    "sources_status": status_list,
                    "total_count": len(status_list)
                }, "所有数据源状态获取成功")
                
        except HTTPError:
            raise
        except Exception as e:
            logger.error(f"获取数据源状态失败: {str(e)}")
            raise HTTPError(500, f"获取数据源状态失败: {str(e)}")


class DataSourceQueryHandler(BaseHandler):
    """数据源查询处理器"""

    @inject
    def initialize(
        self,
        data_source_manager: DataSourceManager = Provide[Container.data_source_manager]
    ):
        """初始化处理器"""
        super().initialize()
        self.data_source_manager: DataSourceManager = data_source_manager
        
    async def post(self):
        """查询数据源（复杂查询）"""
        try:
            if not self.json_body:
                raise HTTPError(400, "缺少请求数据")
                
            request = DataSourceQueryRequest(**self.json_body)
            
            logger.info(f"执行数据源查询: {request.query}")
            
            # 执行查询
            # 将字符串转换为 DataSourceType 枚举
            source_types = None
            if request.source_types:
                source_types = []
                for st in request.source_types:
                    # 直接通过枚举值查找
                    for source_type in DataSourceType:
                        if source_type.value == st.lower():
                            source_types.append(source_type)
                            break

            results, _ = await self.data_source_manager.collect_all_data(
                query=request.query,
                filters={**request.filters, "limit": request.limit},
                source_types=source_types
            )

            # 按数据源分组结果
            grouped_results = {}
            for result in results:
                source_name = result.source_type.value
                if source_name not in grouped_results:
                    grouped_results[source_name] = []
                grouped_results[source_name].append(result.model_dump())
                
            response_data = {
                "query": request.query,
                "results_by_source": grouped_results,
                "total_results": len(results),
                "sources_queried": len(grouped_results)
            }
            
            self.success_response(response_data, "数据源查询成功")
            
        except Exception as e:
            logger.error(f"查询数据源失败: {str(e)}")
            raise HTTPError(500, f"查询数据源失败: {str(e)}")




