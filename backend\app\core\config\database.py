from pydantic import Field, field_validator
from urllib.parse import quote_plus, quote
import re

from .base import BaseAppConfig

def validate_hostname(hostname: str) -> bool:
    """验证主机名格式"""
    hostname_pattern = re.compile(
        r'^(?:[a-zA-Z0-9]'           # 首字符
        r'(?:[a-zA-Z0-9-]{0,61}'     # 中间字符
        r'[a-zA-Z0-9])?\.)*'         # 域名部分
        r'[a-zA-Z0-9]'               # 首字符
        r'(?:[a-zA-Z0-9-]{0,61}'     # 中间字符
        r'[a-zA-Z0-9])?$'            # 结尾字符
        r'|localhost$'                # 或者是 localhost
    )
    return bool(hostname_pattern.match(hostname))

def validate_database_name(name: str) -> bool:
    """验证数据库名称格式"""
    db_name_pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
    return bool(db_name_pattern.match(name))

def validate_port(port: str) -> bool:
    """验证端口号"""
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except ValueError:
        return False

class PostgreSQLSettings(BaseAppConfig):
    """PostgreSQL 数据库配置"""
    POSTGRESQL_HOST: str = Field(
        default="localhost",
        description="PostgreSQL服务器地址"
    )
    POSTGRESQL_USER: str = Field(
        default="gugu_apex",
        description="PostgreSQL用户名"
    )
    POSTGRESQL_PASSWORD: str = Field(
        default="gugu_apex_pass",
        description="PostgreSQL密码"
    )
    POSTGRESQL_DB: str = Field(
        default="gugu_apex",
        description="PostgreSQL数据库名"
    )
    POSTGRESQL_PORT: str = Field(
        default="5432",
        description="PostgreSQL端口号"
    )
    
    # 数据库引擎配置
    POSTGRESQL_ECHO_SQL: bool = Field(
        default=True,
        description="是否打印SQL语句"
    )
    POSTGRESQL_POOL_SIZE: int = Field(
        default=20,
        description="连接池大小"
    )
    POSTGRESQL_MAX_OVERFLOW: int = Field(
        default=10,
        description="最大溢出连接数"
    )
    POSTGRESQL_POOL_TIMEOUT: int = Field(
        default=30,
        description="连接池超时时间（秒）"
    )
    POSTGRESQL_POOL_RECYCLE: int = Field(
        default=3600,
        description="连接回收时间（秒）"
    )
    POSTGRESQL_POOL_PRE_PING: bool = Field(
        default=True,
        description="是否启用连接预检测"
    )
    
    @field_validator('POSTGRESQL_HOST')
    @classmethod
    def validate_server(cls, v: str) -> str:
        if not validate_hostname(v):
            raise ValueError("无效的服务器地址")
        return v
    
    @field_validator('POSTGRESQL_DB')
    @classmethod
    def validate_db(cls, v: str) -> str:
        if not validate_database_name(v):
            raise ValueError("无效的数据库名称")
        return v
    
    @field_validator('POSTGRESQL_PORT')
    @classmethod
    def validate_port_number(cls, v: str) -> str:
        if not validate_port(v):
            raise ValueError("无效的端口号")
        return v
    
    @field_validator('POSTGRESQL_POOL_SIZE', 'POSTGRESQL_MAX_OVERFLOW', 'POSTGRESQL_POOL_TIMEOUT', 'POSTGRESQL_POOL_RECYCLE')
    @classmethod
    def validate_positive_int(cls, v: int) -> int:
        """验证正整数配置项
        
        Args:
            v: 要验证的值
            
        Returns:
            int: 验证后的值
            
        Raises:
            ValueError: 当值小于等于0时
        """
        if v <= 0:
            raise ValueError("值必须大于0")
        return v
    
    @property
    def DATABASE_URL(self) -> str:
        """获取数据库连接URL"""
        return (
            f"postgresql://"
            f"{quote(self.POSTGRESQL_USER)}:"
            f"{quote_plus(self.POSTGRESQL_PASSWORD)}@"
            f"{quote(self.POSTGRESQL_HOST)}:"
            f"{self.POSTGRESQL_PORT}/"
            f"{quote(self.POSTGRESQL_DB)}"
        )
    

class RedisSettings(BaseAppConfig):
    """Redis 配置"""
    REDIS_HOST: str = Field(
        default="localhost",
        description="Redis服务器地址"
    )
    REDIS_PORT: int = Field(
        default=6379,
        description="Redis端口号",
        ge=1,
        le=65535
    )
    REDIS_DB: int = Field(
        default=0,
        description="Redis数据库编号",
        ge=0
    )
    REDIS_PASSWORD: str = Field(
        default="",
        description="Redis密码"
    )

    @field_validator('REDIS_HOST')
    @classmethod
    def validate_host(cls, v: str) -> str:
        if not validate_hostname(v):
            raise ValueError("无效的Redis主机名")
        return v

    @property
    def REDIS_URL(self) -> str:
        """获取Redis连接URL"""
        base_url = f"redis://{quote(self.REDIS_HOST)}:{self.REDIS_PORT}/{self.REDIS_DB}"
        if self.REDIS_PASSWORD:
            return f"redis://:{quote_plus(self.REDIS_PASSWORD)}@{quote(self.REDIS_HOST)}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return base_url
    

class MongoSettings(BaseAppConfig):
    """MongoDB 配置"""
    MONGODB_HOST: str = Field(
        default="localhost",
        description="MongoDB服务器地址"
    )
    MONGODB_PORT: int = Field(
        default=27017,
        description="MongoDB端口号",
        ge=1,
        le=65535
    )
    MONGODB_DB: str = Field(
        default="gugu_apex",
        description="MongoDB数据库名"
    )
    MONGODB_USER: str = Field(
        default="gugu_apex",
        description="MongoDB用户名"
    )
    MONGODB_PASSWORD: str = Field(
        default="gugu_apex_pass",
        description="MongoDB密码"
    )

    @field_validator('MONGODB_HOST')
    @classmethod
    def validate_host(cls, v: str) -> str:
        if not validate_hostname(v):
            raise ValueError("无效的MongoDB主机名")
        return v

    @field_validator('MONGODB_DB')
    @classmethod
    def validate_db(cls, v: str) -> str:
        if not validate_database_name(v):
            raise ValueError("无效的数据库名称")
        return v

    @property
    def MONGODB_URL(self) -> str:
        """获取MongoDB连接URL"""
        if self.MONGODB_USER and self.MONGODB_PASSWORD:
            auth_part = f"{quote(self.MONGODB_USER)}:{quote_plus(self.MONGODB_PASSWORD)}@"
        else:
            auth_part = ""
        
        return (
            f"mongodb://"
            f"{auth_part}"
            f"{quote(self.MONGODB_HOST)}:"
            f"{self.MONGODB_PORT}/"
            f"{quote(self.MONGODB_DB)}"
        )
