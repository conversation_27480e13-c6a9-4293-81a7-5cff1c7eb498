#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/27 15:17
# @File    : github_flow.py
# @Description: 
"""

import random
from typing import List, Optional, Dict
from datetime import datetime
from typing import Optional,List
from typing_extensions import Self
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum

class ProjectStatusItem(BaseModel):
    """项目状态项"""
    id: str = Field(..., description="项目ID")
    name: str = Field(..., description="项目名称")
    repository_url: str = Field(..., description="仓库URL")
    project_phase: str = Field(..., description="项目阶段")
    status: str = Field(..., description="项目状态")
    stars: Optional[str] = Field(None, description="星数")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class ProjectStatusListResponse(BaseModel):
    """项目状态列表分页响应"""
    total: int = Field(..., description="总记录数")
    projects: List[ProjectStatusItem] = Field(..., description="项目列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(0, description="总页数")
    status_statistics: Dict[str, int] = Field(..., description="各状态统计")

    @model_validator(mode="after")
    def compute_total_pages(self) -> Self:
        """计算总页数"""
        if self.total and self.page_size and self.total > 0 and self.page_size > 0:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 0
        return self

    class Config:
        """配置"""
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }