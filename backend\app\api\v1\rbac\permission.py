"""
权限管理API处理器
"""
from typing import List, Optional
from tornado.web import HTTPError
import structlog
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.middleware import require_auth
from app.core.di.containers import Container
from app.services.rbac.permission import PermissionService
from app.schemas.rbac.permission import (
    PermissionCreateRequest,
    PermissionUpdateRequest,
    Permission,
    PermissionListResponse,
)

logger = structlog.get_logger(__name__)

class PermissionHandler(BaseHandler):
    """权限管理处理器"""
    
    @inject
    def initialize(
        self,
        permission_service: PermissionService = Provide[Container.permission_service]
    ) -> None:
        """初始化处理器
        
        Args:
            permission_service: 权限服务
        """
        super().initialize()
        self.permission_service = permission_service
    
    @require_auth(required=True, permissions=["permission:list"])
    async def get(self) -> None:
        """获取权限列表"""
        try:
            # 获取查询参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            search = self.get_argument("search", None)

            # 获取权限列表
            result = await self.permission_service.get_list(
                page=page,
                page_size=page_size,
                search=search
            )
            self.success_response(result.model_dump())
                
        except ValueError as e:
            raise HTTPError(400, str(e))
        except Exception as e:
            logger.error("获取权限列表失败", error=str(e))
            raise HTTPError(500, "获取权限列表失败")
    
    @require_auth(required=True, permissions=["permission:create"])
    async def post(self) -> None:
        """创建权限"""
        try:
            # 验证请求数据
            data = PermissionCreateRequest(**self.json_body)
            
            # 调用服务创建权限
            permission = await self.permission_service.create(data)
            self.success_response(permission.model_dump())
            
        except ValueError as e:
            self.write_error(500,error_message=str(e))
            return
        except Exception as e:
            logger.error("创建权限失败", error=str(e))
            raise HTTPError(500, "创建权限失败")


class PermissionDetailHandler(BaseHandler):
    """权限详情处理器"""
    
    @inject
    def initialize(
        self,
        permission_service: PermissionService = Provide[Container.permission_service]
    ) -> None:
        """初始化处理器
        
        Args:
            permission_service: 权限服务
        """
        super().initialize()
        self.permission_service = permission_service
    
    @require_auth(required=True, permissions=["permission:detail"])
    async def get(self, permission_id: str) -> None:
        """获取权限详情
        
        Args:
            permission_id: 权限ID
        """
        try:
            # 调用服务获取权限详情
            permission = await self.permission_service.get_by_id(permission_id)
            if not permission:
                self.write_error(500, "权限不存在")
                return
            self.success_response(permission.model_dump())

        except ValueError as e:
            self.write_error(500, error_message=str(e))
            return
        except Exception as e:
            logger.error("获取权限详情失败", error=str(e))
            raise HTTPError(500, "获取权限详情失败 " + str(e))


    @require_auth(required=True, permissions=["permission:update"])
    async def put(self, permission_id: str) -> None:
        """更新权限
        
        Args:
            permission_id: 权限ID
        """
        try:
            # 验证请求数据
            data = PermissionUpdateRequest(**self.json_body)
            data.permission_id = permission_id
            
            # 调用服务更新权限
            permission = await self.permission_service.update(data)
            if not permission:
                self.write_error(500, "权限不存在")
                return
            self.success_response(permission.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
            return
        except Exception as e:
            logger.error("更新权限失败", error=str(e))
            raise HTTPError(500, "更新权限失败")
    
    @require_auth(required=True, permissions=["permission:delete"])
    async def delete(self, permission_id: str) -> None:
        """删除权限
        
        Args:
            permission_id: 权限ID
        """
        try:
            # 调用服务删除权限
            deleted = await self.permission_service.delete(permission_id)
            
            self.success_response()

        except ValueError as e:
            self.write_error(500, error_message=str(e))
            return
        except Exception as e:
            logger.error("删除权限失败", error=str(e))
            raise HTTPError(500, "删除权限失败")
