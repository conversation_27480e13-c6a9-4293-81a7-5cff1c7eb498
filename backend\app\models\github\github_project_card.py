"""
GitHub项目数据库模型
"""
from datetime import datetime
import uuid
from sqlalchemy import Column, String, Text, JSON, DateTime, ForeignKey, Boolean
from sqlalchemy.ext.mutable import MutableDict
from datetime import datetime, timezone
from sqlalchemy import String,DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.models.model_base import ModelBase


class GitHubProjectCardModel(ModelBase):
    """GitHub项目表"""
    __tablename__ = "github_projects_cards"

    project_id: Mapped[str] = mapped_column(String(32), comment='项目id')
    title: Mapped[str] = mapped_column(String(256), comment='标题')
    content: Mapped[str] = mapped_column(Text, comment='内容')
    like: Mapped[int] = mapped_column(default=0, comment='喜欢')
    dislike: Mapped[int] = mapped_column(default=0, comment='不喜欢')
    collect: Mapped[int] = mapped_column(default=0, comment='收藏')
    sort_order: Mapped[int] = mapped_column(default=0, comment='排序号')

    def __repr__(self):
        return f"<GitHubProjectCard {self.name}>"