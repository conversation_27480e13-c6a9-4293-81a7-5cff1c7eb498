"""
评论API处理器
"""
from typing import Dict, Any, Optional, List
import structlog
from tornado.web import H<PERSON><PERSON><PERSON><PERSON>r
from pydantic import BaseModel, Field
from datetime import datetime, timezone
from sqlalchemy import select

from app.api.base import BaseHandler
from app.schemas.article.comment import (
    CommentCreate,
    CommentAuditRequest,
    CommentDeleteRequest
)
from app.services.article.article_service import ArticleService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.services.rbac.user import UserService
from app.utils.security import get_current_user_id
from app.models.article.comment import CommentModel

logger = structlog.get_logger(__name__)


class ArticleCommentTreeHandler(BaseHandler):
    """获取文章评论树处理器"""

    @inject
    def initialize(
            self,
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    async def get(self) -> None:
        """获取文章评论树

        Args:
            article_id: 文章ID
            page: 页码
            page_size: 每页大小
            depth: 评论树深度 (可选，默认3)
            comment_id: 指定的评论ID (可选)
        """
        try:
            # 获取分页参数
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "20")
            article_id = self.get_argument("article_id", None)
            
            # 获取新增的参数
            depth_str = self.get_argument("depth", "3")
            comment_id = self.get_argument("comment_id", None)
            search = self.get_argument("search", None)

            # 转换参数
            try:
                page = int(page_str)
                page_size = int(page_size_str)
                depth = int(depth_str)
            except ValueError:
                logger.error("参数必须是数字", page=page_str, page_size=page_size_str, depth=depth_str)
                self.write_error(500, error_message="参数必须是数字")
                return

            # 验证参数
            if page < 1 or page_size < 1 or page_size > 999:
                logger.error("分页参数无效", page=page, page_size=page_size)
                self.write_error(500, error_message="分页参数无效")
                return
            
            # 验证深度参数
            if depth < 1 or depth > 10:
                logger.error("深度参数无效", depth=depth)
                self.write_error(500, error_message="深度参数必须在1-10之间")
                return

            # 调用服务获取评论树
            comment_tree = await self.article_service.get_article_comments(
                article_id=article_id,
                page=page,
                size=page_size,
                depth=depth,
                comment_id=comment_id,
                search=search
            )

            # 返回结果
            self.success_response(comment_tree.dict())

        except Exception as e:
            logger.error("获取文章评论树失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class ArticleCommentCreateHandler(BaseHandler):
    """发布文章评论处理器"""

    @inject
    def initialize(
            self,
            article_service: ArticleService = Provide[Container.article_service],
            user_service: UserService = Provide[Container.user_service]
    ):
        """初始化处理器

        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        self.user_service = user_service
        super().initialize()

    # @require_auth(required=True)
    async def post(self) -> None:

        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 从请求体获取评论数据
            request_data = self.json_body
            if not request_data:
                logger.error("请求体为空")
                self.write_error(500, error_message="请求体不能为空")
                return
            if request_data.get("parent_id") == "":
                request_data["parent_id"] = None

            try:
                comment_data = CommentCreate(**request_data)
            except ValueError as e:
                logger.error("请求数据无效", error=str(e), request_data=request_data)
                self.write_error(500, error_message=f"请求数据无效: {str(e)}")
                return
            user = await self.user_service.get_by_id(user_id)
            # 获取用户信息（可以从token中获取或从数据库查询）
            if not user:
                raise ValueError("找不到用户")
            user_name = user.nickname if user.nickname and user.nickname.strip() != "" else user.username
            user_avatar = None  # 可以从token或数据库获取

            # 调用服务创建评论
            comment = await self.article_service.create_article_comment(
                article_id=comment_data.project_id,
                comment_data=comment_data,
                user_id=user_id,
                user_name=user_name,
                user_avatar=user_avatar
            )

            # 返回结果
            self.success_response(comment.dict())

        except Exception as e:
            logger.error("发布评论失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class ArticleCommentAuditHandler(BaseHandler):
    """审核文章评论处理器"""

    @inject
    def initialize(
            self,
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    # @require_auth(required=True)
    async def put(self) -> None:
        """审核文章评论

        Args:
            comment_id: 评论ID
        """
        try:
            # 获取当前用户ID（审核人）
            auth_header = self.request.headers.get('Authorization')
            auditor_id = get_current_user_id(auth_header)

            # 从请求体获取审核数据
            request_data = self.json_body
            if not request_data:
                logger.error("请求体为空")
                self.write_error(500, error_message="请求体不能为空")
                return

            try:
                audit_data = CommentAuditRequest(**request_data)
            except ValueError as e:
                logger.error("请求数据无效", error=str(e), request_data=request_data)
                self.write_error(500, error_message=f"请求数据无效: {str(e)}")
                return

            # 验证审核状态
            if audit_data.status not in ["approved", "rejected"]:
                logger.error("审核状态无效", status=audit_data.status)
                self.write_error(500, error_message="审核状态必须是 approved 或 rejected")
                return

            # 调用服务审核评论
            comment = await self.article_service.audit_article_comment(
                comment_id=audit_data.comment_id,
                audit_data=audit_data,
                auditor_id=auditor_id
            )

            if not comment:
                logger.error("评论不存在", comment_id=audit_data.comment_id)
                self.write_error(500, error_message="评论不存在")
                return

            # 返回结果
            self.success_response(comment.dict())

        except Exception as e:
            logger.error("审核评论失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")


class ArticleCommentDeleteHandler(BaseHandler):
    """删除文章评论处理器"""

    @inject
    def initialize(
            self,
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    async def delete(self) -> None:
        """删除文章评论
        
        请求参数：
            comment_id: 评论ID (URL参数或请求体)
            delete_type: 删除类型，soft/hard (可选，默认soft)
            cascade: 是否级联删除子评论 (可选，默认True)
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            
            if not user_id:
                logger.error("用户未登录")
                self.write_error(500, error_message="用户未登录")
                return

            # 获取评论ID - 优先从URL参数获取，其次从请求体
            comment_id = self.get_argument("comment_id", None)
            
            if not comment_id:
                # 从请求体获取
                request_data = self.json_body
                if request_data:
                    comment_id = request_data.get("comment_id")
            
            if not comment_id:
                logger.error("缺少评论ID参数")
                self.write_error(500, error_message="缺少评论ID参数")
                return

            # 获取删除选项
            delete_type = self.get_argument("delete_type", "soft")
            cascade_str = self.get_argument("cascade", "true")
            
            # 从请求体获取参数（如果URL参数不存在）
            request_data = self.json_body
            if request_data:
                delete_type = request_data.get("delete_type", delete_type)
                cascade_str = str(request_data.get("cascade", cascade_str))

            # 参数验证
            if delete_type not in ["soft", "hard"]:
                logger.error("删除类型无效", delete_type=delete_type)
                self.write_error(500, error_message="删除类型必须是 soft 或 hard")
                return
                
            cascade = cascade_str.lower() in ["true", "1", "yes"]

            # 调用服务删除评论
            success = await self.article_service.delete_article_comment(
                comment_id=comment_id,
                user_id=user_id,
                # delete_type=delete_type,
                cascade=cascade
            )

            if success:
                self.success_response({
                    "message": "评论删除成功",
                    "comment_id": comment_id,
                    "delete_type": delete_type,
                    "cascade": cascade
                })
            else:
                self.write_error(500, error_message="删除评论失败")

        except ValueError as e:
            # 业务逻辑错误（如权限不足、评论不存在等）
            logger.error("删除评论业务错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("删除评论失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"服务器内部错误: {str(e)}")