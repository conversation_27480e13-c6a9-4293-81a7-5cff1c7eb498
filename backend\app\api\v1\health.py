"""
健康检查API
"""
from datetime import datetime, timezone
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.config import settings
from app.schemas.health import HealthResponse

logger = structlog.get_logger(__name__)

class HealthHandler(BaseHandler):
    """健康检查处理器"""
    
    async def get(self) -> None:
        """获取服务健康状态"""
        try:
            # 构建健康检查数据
            result = HealthResponse(
                status="healthy",
                timestamp=datetime.now(timezone.utc),
                version=settings.app.VERSION,
                service=settings.app.PROJECT_NAME,
                environment="development" if settings.app.DEBUG else "production"
            ).model_dump()
            
            self.success_response(result)
            
        except Exception as e:
            logger.error("健康检查失败", error=str(e))
            self.write_error(500, error_message="健康检查失败")
            return