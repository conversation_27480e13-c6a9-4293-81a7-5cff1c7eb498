#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型管理API处理器
"""
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.services.ai_chat import AIChatService
from app.schemas.ai_chat import ModelListResponseSchema, ModelConfigResponseSchema
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


class ModelListHandler(BaseHandler):
    """模型列表处理器"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def get(self) -> None:
        """获取可用模型列表"""
        try:
            # 获取可用模型列表
            model_names = self.chat_service.get_available_models()
            
            # 构建模型详情列表
            models = []
            for model_name in model_names:
                config = self.chat_service.get_model_config(model_name)
                if config:
                    model_info = {
                        "key": model_name,
                        "name": config.model_name,
                        "provider": config.model_provider.value,
                        "description": f"{config.model_provider.value} - {config.model_name}",
                        "max_tokens": config.max_tokens,
                        "temperature": config.temperature,
                        "streaming": config.streaming,
                        "capabilities": ["text_generation", "conversation"]
                    }
                    models.append(model_info)
            
            response_data = ModelListResponseSchema(
                models=models,
                total=len(models)
            )
            
            self.success_response(response_data.model_dump(), "获取模型列表成功")
            
        except Exception as e:
            logger.error("获取模型列表失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取模型列表失败: {str(e)}")


class ModelConfigHandler(BaseHandler):
    """模型配置处理器"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def get(self, model_key: str) -> None:
        """获取模型配置"""
        try:
            # 获取模型配置
            config = self.chat_service.get_model_config(model_key)
            if not config:
                raise HTTPError(404, "模型配置不存在")
            
            # 构建响应数据
            response_data = ModelConfigResponseSchema(
                key=model_key,
                model_provider=config.model_provider.value,
                model_name=config.model_name,
                api_key=config.api_key,
                base_url=config.base_url,
                max_tokens=config.max_tokens,
                temperature=config.temperature,
                streaming=config.streaming,
                system_prompt=config.system_prompt,
                request_timeout=config.request_timeout,
                max_retries=config.max_retries,
                additional_params=config.additional_params
            )
            
            self.success_response(response_data.model_dump(), "获取模型配置成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("获取模型配置失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取模型配置失败: {str(e)}")


class ModelProviderHandler(BaseHandler):
    """模型提供商处理器"""
    
    async def get(self) -> None:
        """获取支持的模型提供商列表"""
        try:
            providers = [
                {
                    "key": "openai",
                    "name": "OpenAI",
                    "description": "OpenAI官方API",
                    "supported_models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"]
                },
                {
                    "key": "anthropic", 
                    "name": "Anthropic",
                    "description": "Anthropic Claude系列模型",
                    "supported_models": ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"]
                },
                {
                    "key": "google",
                    "name": "Google",
                    "description": "Google AI模型",
                    "supported_models": ["gemini-pro", "gemini-pro-vision"]
                },
                {
                    "key": "openrouter",
                    "name": "OpenRouter",
                    "description": "OpenRouter聚合API服务",
                    "supported_models": [
                        "google/gemma-3-27b-it:free",
                        "openai/gpt-4o-mini",
                        "anthropic/claude-3.5-sonnet",
                        "meta-llama/llama-3.1-8b-instruct:free"
                    ]
                },
                {
                    "key": "azure",
                    "name": "Azure OpenAI",
                    "description": "微软Azure OpenAI服务",
                    "supported_models": ["gpt-35-turbo", "gpt-4"]
                },
                {
                    "key": "custom",
                    "name": "自定义",
                    "description": "自定义API端点",
                    "supported_models": []
                }
            ]
            
            response_data = {
                "providers": providers,
                "total": len(providers)
            }
            
            self.success_response(response_data, "获取模型提供商列表成功")
            
        except Exception as e:
            logger.error("获取模型提供商列表失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"获取模型提供商列表失败: {str(e)}")



