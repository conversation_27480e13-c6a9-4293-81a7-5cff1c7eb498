#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI对话API处理器
"""
import json
import asyncio
from datetime import datetime
from typing import Optional
import structlog
from tornado.web import HTTPError
from tornado.websocket import WebSocketHandler

from app.api.base import BaseHandler
from app.services.ai_chat import AIChatService, ChatRequest, ChatConfig, ModelProvider
from app.schemas.ai_chat import (
    ChatRequestSchema, ChatResponseSchema, StreamChatResponseSchema,
    ChatConfigSchema
)
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


def serialize_datetime_in_dict(data):
    """递归地将字典中的datetime对象转换为ISO字符串"""
    if isinstance(data, dict):
        return {key: serialize_datetime_in_dict(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [serialize_datetime_in_dict(item) for item in data]
    elif isinstance(data, datetime):
        return data.isoformat()
    else:
        return data


class ChatHandler(BaseHandler):
    """AI对话处理器（非流式）"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.chat_service = ai_chat_service
    
    async def post(self) -> None:
        """发送聊天消息"""
        try:
            # 验证请求数据
            if not self.json_body:
                raise HTTPError(400, "请求体不能为空")
            
            # 解析请求数据
            try:
                request_data = ChatRequestSchema(**self.json_body)
            except Exception as e:
                logger.error("请求数据验证失败", error=str(e))
                raise HTTPError(400, f"请求数据格式错误: {str(e)}")
            
            # 构建聊天配置
            config = None
            if request_data.config:
                config = ChatConfig(
                    model_provider=ModelProvider(request_data.config.model_provider),
                    model_name=request_data.config.model_name,
                    api_key=request_data.config.api_key,
                    base_url=request_data.config.base_url,
                    max_tokens=request_data.config.max_tokens,
                    temperature=request_data.config.temperature,
                    streaming=False,  # 非流式
                    system_prompt=request_data.config.system_prompt,
                    request_timeout=request_data.config.request_timeout,
                    max_retries=request_data.config.max_retries,
                    additional_params=request_data.config.additional_params or {}
                )
            
            # 构建聊天请求
            chat_request = ChatRequest(
                session_id=request_data.session_id,
                message=request_data.message,
                config=config,
                stream=False,
                user_id=request_data.user_id,
                metadata=request_data.metadata or {}
            )
            
            # 执行对话
            response = await self.chat_service.chat(chat_request)
            
            # 构建响应数据
            response_data = ChatResponseSchema(
                session_id=response.session_id,
                message_id=response.message_id,
                content=response.content,
                model_used=response.model_used,
                tokens_used=response.tokens_used,
                finish_reason=response.finish_reason,
                metadata=response.metadata,
                timestamp=response.timestamp
            )
            
            self.success_response(response_data.model_dump(), "对话成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error("对话处理失败", error=str(e), exc_info=True)
            raise HTTPError(500, f"对话处理失败: {str(e)}")


class ChatStreamHandler(WebSocketHandler):
    """AI对话WebSocket处理器（流式）"""

    @inject
    def initialize(
        self,
        ai_chat_service: AIChatService = Provide[Container.ai_chat_service]
    ):
        """初始化处理器"""
        self.chat_service = ai_chat_service
        self.current_task = None

    def check_origin(self, origin):
        """检查WebSocket连接来源"""
        # 在生产环境中应该更严格地检查来源
        del origin  # 忽略未使用的参数
        return True

    async def open(self):
        """WebSocket连接建立"""
        logger.info("WebSocket连接已建立", remote_ip=self.request.remote_ip)

        # 发送连接成功消息
        await self.write_message({
            "type": "connection",
            "status": "connected",
            "message": "WebSocket连接已建立"
        })

    async def on_message(self, message):
        """处理WebSocket消息"""
        try:
            # 解析消息
            try:
                data = json.loads(message)
            except json.JSONDecodeError as e:
                await self.write_message({
                    "type": "error",
                    "message": f"消息格式错误: {str(e)}"
                })
                return

            # 检查消息类型
            msg_type = data.get("type")

            if msg_type == "chat":
                await self._handle_chat_message(data)
            elif msg_type == "cancel":
                await self._handle_cancel_message()
            else:
                await self.write_message({
                    "type": "error",
                    "message": f"未知的消息类型: {msg_type}"
                })

        except Exception as e:
            logger.error("处理WebSocket消息失败", error=str(e), exc_info=True)
            await self.write_message({
                "type": "error",
                "message": f"处理消息失败: {str(e)}"
            })

    async def _handle_chat_message(self, data):
        """处理聊天消息"""
        try:
            # 如果有正在进行的任务，先取消
            if self.current_task and not self.current_task.done():
                self.current_task.cancel()

            # 验证请求数据
            try:
                request_data = ChatRequestSchema(**data.get("data", {}))
            except Exception as e:
                await self.write_message({
                    "type": "error",
                    "message": f"请求数据格式错误: {str(e)}"
                })
                return

            # 构建聊天配置
            config = None
            if request_data.config:
                config = ChatConfig(
                    model_provider=ModelProvider(request_data.config.model_provider),
                    model_name=request_data.config.model_name,
                    api_key=request_data.config.api_key,
                    base_url=request_data.config.base_url,
                    max_tokens=request_data.config.max_tokens,
                    temperature=request_data.config.temperature,
                    streaming=True,
                    system_prompt=request_data.config.system_prompt,
                    request_timeout=request_data.config.request_timeout,
                    max_retries=request_data.config.max_retries,
                    additional_params=request_data.config.additional_params or {}
                )

            # 构建聊天请求
            chat_request = ChatRequest(
                session_id=request_data.session_id,
                message=request_data.message,
                config=config,
                stream=True,
                user_id=request_data.user_id,
                metadata=request_data.metadata or {}
            )

            # 创建流式对话任务
            self.current_task = asyncio.create_task(self._stream_chat(chat_request))

        except Exception as e:
            logger.error("处理聊天消息失败", error=str(e), exc_info=True)
            await self.write_message({
                "type": "error",
                "message": f"处理聊天消息失败: {str(e)}"
            })

    async def _stream_chat(self, chat_request):
        """执行流式对话"""
        try:
            # 发送开始消息
            await self.write_message({
                "type": "chat_start",
                "message": "开始生成回复..."
            })

            # 执行流式对话
            async for chunk in self.chat_service.chat_stream(chat_request):
                # 检查连接是否还活跃
                if self.ws_connection is None:
                    logger.info("WebSocket连接已断开，停止流式输出")
                    break

                # 构建流式响应数据
                chunk_data = StreamChatResponseSchema(
                    session_id=chunk.session_id,
                    message_id=chunk.message_id,
                    delta=chunk.delta,
                    is_complete=chunk.is_complete,
                    model_used=chunk.model_used,
                    metadata=chunk.metadata,
                    timestamp=chunk.timestamp
                )

                # 序列化数据并处理datetime
                chunk_dict = chunk_data.model_dump()
                serialized_data = serialize_datetime_in_dict(chunk_dict)

                # 发送流式数据
                await self.write_message({
                    "type": "chat_chunk",
                    "data": serialized_data
                })

                # 如果对话完成，发送完成消息
                if chunk.is_complete:
                    await self.write_message({
                        "type": "chat_complete",
                        "message": "对话完成"
                    })
                    break

        except asyncio.CancelledError:
            logger.info("流式对话任务被取消")
            await self.write_message({
                "type": "chat_cancelled",
                "message": "对话已取消"
            })
        except Exception as e:
            logger.error("流式对话失败", error=str(e), exc_info=True)
            await self.write_message({
                "type": "error",
                "message": f"对话失败: {str(e)}"
            })

    async def _handle_cancel_message(self):
        """处理取消消息"""
        if self.current_task and not self.current_task.done():
            self.current_task.cancel()
            await self.write_message({
                "type": "cancelled",
                "message": "当前对话已取消"
            })
        else:
            await self.write_message({
                "type": "info",
                "message": "没有正在进行的对话"
            })

    def on_close(self):
        """WebSocket连接关闭"""
        logger.info("WebSocket连接已关闭")

        # 取消正在进行的任务
        if self.current_task and not self.current_task.done():
            self.current_task.cancel()
