#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文章生成 - 交互式规划API
"""

import asyncio
import json
import logging
from uuid import uuid4
from typing import Any, Optional
from tornado.websocket import WebSocketHandler
from dependency_injector.wiring import Provide, inject

from app.core.di.containers import Container
from app.services.reasoning.intelligent_article_generator.interfaces.interactive_planning_api import (
    InteractivePlanningAPI,
    StartPlanningRequest,
    UserFeedbackRequest,
    ContinueConversationRequest,
    FinalizePlanningRequest
)

logger = logging.getLogger(__name__)


class InteractivePlanningWebSocketHandler(WebSocketHandler):
    """交互式规划WebSocket处理器"""

    @inject
    def initialize(
        self,
        api: InteractivePlanningAPI = Provide[Container.interactive_planning_api]
    ):
        """初始化处理器"""
        self.session_id: Optional[str] = str(uuid4())
        self.api: InteractivePlanningAPI = api
        self.stream_queue: Optional[asyncio.Queue] = None
        self.stream_task: Optional[asyncio.Task] = None
        
    def check_origin(self, origin) -> bool:
        """检查WebSocket连接来源"""
        # 在生产环境中应该更严格地检查来源
        return True
        
    def open(self):
        """WebSocket连接打开"""
        logger.info(f"WebSocket连接已建立: {self.request.remote_ip}")

        # 设置流回调
        self._setup_stream_callback()

        self.write_message({
            "type": "connection",
            "message": "WebSocket连接已建立",
            "timestamp": self._get_timestamp()
        })
        
    async def on_message(self, message):
        """处理WebSocket消息"""
        try:
            data = json.loads(message)
            message_type = data.get("type")
            
            logger.info(f"收到WebSocket消息: {message_type}")
            
            if message_type == "start_planning":
                request_data = data.get("data", {})
                request = StartPlanningRequest(**request_data)
                await self._handle_start_planning(request)
            elif message_type == "submit_feedback":
                request_data = data.get("data", {})
                if not request_data.get("session_id"):
                    request_data["session_id"] = self.session_id
                request = UserFeedbackRequest(**request_data)
                await self._handle_submit_feedback(request)
            elif message_type == "continue_conversation":
                request_data = data.get("data", {})
                if not request_data.get("session_id"):
                    request_data["session_id"] = self.session_id
                request = ContinueConversationRequest(**request_data)
                await self._handle_continue_conversation(request)
            elif message_type == "finalize_planning":
                request_data = data.get("data", {})
                if not request_data.get("session_id"):
                    request_data["session_id"] = self.session_id
                request = FinalizePlanningRequest(**request_data)
                await self._handle_finalize_planning(request)
            elif message_type == "get_status":
                session_id = data.get("session_id") or self.session_id
                await self._handle_get_status(session_id)
            else:
                await self._send_error("未知的消息类型", f"不支持的消息类型: {message_type}")
                
        except json.JSONDecodeError:
            await self._send_error("JSON解析错误", "消息格式不正确")
        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {str(e)}")
            await self._send_error("处理失败", str(e))
            
    def on_close(self):
        """WebSocket连接关闭"""
        logger.info(f"WebSocket连接已关闭: {self.request.remote_ip}")

        # 清理流任务
        self._cleanup_stream_task()
        
    async def _handle_start_planning(self, request: StartPlanningRequest):
        """处理开始规划请求"""
        try:
            # 启动流数据监听
            self._start_stream_monitoring()

            response = await self.api.start_planning(request)

            if response.success:
                self.session_id = response.data.get("session_id")
                await self._send_success("start_planning", response.data, response.message)
            else:
                await self._send_error("开始规划失败", response.error or response.message)

        except Exception as e:
            await self._send_error("开始规划失败", str(e))
            
    async def _handle_submit_feedback(self, request: UserFeedbackRequest):
        """处理提交反馈请求"""
        try:
            # 启动流数据监听
            self._start_stream_monitoring()

            response = await self.api.submit_feedback(request)

            if response.success:
                await self._send_success("submit_feedback", response.data, response.message)
            else:
                await self._send_error("提交反馈失败", response.error or response.message)

        except Exception as e:
            await self._send_error("提交反馈失败", str(e))
            
    async def _handle_continue_conversation(self, request: ContinueConversationRequest):
        """处理继续对话请求"""
        try:
            # 启动流数据监听
            self._start_stream_monitoring()

            response = await self.api.continue_conversation(request)

            if response.success:
                await self._send_success("continue_conversation", response.data, response.message)
            else:
                await self._send_error("继续对话失败", response.error or response.message)

        except Exception as e:
            await self._send_error("继续对话失败", str(e))
            
    async def _handle_finalize_planning(self, request: FinalizePlanningRequest):
        """处理最终确定规划请求"""
        try:
            # 启动流数据监听
            self._start_stream_monitoring()

            response = await self.api.finalize_planning(request)

            if response.success:
                await self._send_success("finalize_planning", response.data, response.message)
            else:
                await self._send_error("最终确定规划失败", response.error or response.message)

        except Exception as e:
            await self._send_error("最终确定规划失败", str(e))
            
    async def _handle_get_status(self, session_id: Optional[str]):
        """处理获取状态请求"""
        try:
            if not session_id:
                await self._send_error("获取状态失败", "缺少会话ID")
                return

            response = await self.api.get_session_status(session_id)

            if response.success:
                await self._send_success("get_status", response.data, response.message)
            else:
                await self._send_error("获取状态失败", response.error or response.message)

        except Exception as e:
            await self._send_error("获取状态失败", str(e))
            
    async def _send_success(self, message_type: str, data: Any, message: str):
        """发送成功响应"""
        response = {
            "type": "response",
            "message_type": message_type,
            "success": True,
            "data": data,
            "message": message,
            "timestamp": self._get_timestamp()
        }
        self.write_message(response)
        
    async def _send_error(self, error_type: str, error_message: str):
        """发送错误响应"""
        response = {
            "type": "error",
            "error_type": error_type,
            "success": False,
            "message": error_message,
            "timestamp": self._get_timestamp()
        }
        self.write_message(response)
        
    def _setup_stream_callback(self):
        """设置流回调"""
        try:
            # 创建流队列
            self.stream_queue = asyncio.Queue(maxsize=100)

            # 为API设置流回调
            self.api.set_stream_callback(self.stream_queue)

            logger.info("流回调设置成功")
        except Exception as e:
            logger.error(f"设置流回调失败: {str(e)}")

    def _start_stream_monitoring(self):
        """启动流数据监听"""
        if self.stream_task and not self.stream_task.done():
            # 如果已有监听任务在运行，先取消
            self.stream_task.cancel()

        # 启动新的监听任务
        self.stream_task = asyncio.create_task(self._monitor_stream_events())

    async def _monitor_stream_events(self):
        """监听流事件"""
        try:
            if not self.stream_queue:
                return

            while True:
                try:
                    # 等待流事件，设置超时避免无限等待
                    event = await asyncio.wait_for(
                        self.stream_queue.get(),
                        timeout=30.0
                    )

                    # 处理流事件
                    await self._handle_stream_event(event)

                except asyncio.TimeoutError:
                    # 超时是正常的，继续监听
                    continue
                except asyncio.CancelledError:
                    # 任务被取消，退出循环
                    break

        except Exception as e:
            logger.error(f"监听流事件失败: {str(e)}")

    async def _handle_stream_event(self, event: dict):
        """处理单个流事件"""
        try:
            # 构建流事件消息
            stream_message = {
                "type": "stream_event",
                "event_type": event.get("type", "unknown"),
                "data": event,
                "timestamp": self._get_timestamp()
            }

            # 发送到WebSocket客户端
            self.write_message(stream_message)

            logger.debug(f"发送流事件: {event.get('type')}")

        except Exception as e:
            logger.error(f"处理流事件失败: {str(e)}")

    def _cleanup_stream_task(self):
        """清理流任务"""
        try:
            if self.stream_task and not self.stream_task.done():
                self.stream_task.cancel()
                logger.info("流监听任务已取消")
        except Exception as e:
            logger.error(f"清理流任务失败: {str(e)}")

    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()



