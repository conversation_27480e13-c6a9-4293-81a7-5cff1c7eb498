#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/15 11:25
# @File    : ai_distributed.py
# @Description: 
"""
from pydantic import Field, field_validator
from .base import BaseAppConfig


class AiDistributedSettings(BaseAppConfig):
    """分布式部署配置"""

    DEPLOYMENT_MODE: str = Field(
        default="analyzer",
        description="部署模式：publisher(发布机) 或 analyzer(分析机)"
    )

    AI_ANALYZER_ENDPOINT: str = Field(
        default="",
        description="分析机API地址"
    )

    PUBLISHER_ENDPOINT: str = Field(
        default="",
        description="发布机API地址"
    )

    HEARTBEAT_INTERVAL: int = Field(
        default=30,
        description="心跳间隔（秒）"
    )

    HEARTBEAT_TIMEOUT: int = Field(
        default=120,
        description="心跳超时（秒）"
    )

    def __init__(self, **kwargs):
        """初始化方法，添加日志记录"""
        super().__init__(**kwargs)

        # 使用 print 确保能看到初始化信息
        # print("=" * 50)
        # print("AiDistributedSettings 配置初始化")
        # print(f"部署模式: {self.DEPLOYMENT_MODE}")
        # print(f"分析机地址: {self.AI_ANALYZER_ENDPOINT}")
        # print(f"发布机地址: {self.PUBLISHER_ENDPOINT}")
        # print(f"心跳间隔: {self.HEARTBEAT_INTERVAL} 秒")
        # print(f"心跳超时: {self.HEARTBEAT_TIMEOUT} 秒")
        # print(f"是否为发布机: {self.is_publisher}")
        # print(f"是否为分析机: {self.is_analyzer}")
        # print("AiDistributedSettings 配置初始化完成")
        # print("=" * 50)

    @field_validator("DEPLOYMENT_MODE")
    @classmethod
    def validate_deployment_mode(cls, v: str) -> str:
        valid_modes = ["publisher", "analyzer"]
        if v not in valid_modes:
            raise ValueError(f"部署模式必须是以下之一: {', '.join(valid_modes)}")
        return v

    @property
    def is_publisher(self) -> bool:
        """是否为发布机"""
        return self.DEPLOYMENT_MODE == "publisher"

    @property
    def is_analyzer(self) -> bool:
        """是否为分析机"""
        return self.DEPLOYMENT_MODE == "analyzer"