"""
日志上下文模块
"""
import contextvars
from typing import Any, Dict

import structlog
from structlog.types import EventDict, Processor
from structlog.stdlib import BoundLogger

request_id: contextvars.ContextVar[str] = contextvars.ContextVar("request_id", default="")

def set_request_id(id: str) -> None:
    """设置请求ID

    Args:
        id: 请求ID
    """
    request_id.set(id)

def get_request_id() -> str:
    """获取请求ID

    Returns:
        str: 请求ID
    """
    return request_id.get()

def add_request_id(
    logger: structlog.BoundLogger, method_name: str, event_dict: EventDict
) -> EventDict:
    """添加请求ID到日志上下文

    Args:
        logger: 日志记录器
        method_name: 方法名
        event_dict: 事件字典

    Returns:
        Dict[str, Any]: 更新后的事件字典
    """
    id = get_request_id()
    if id:
        event_dict["request_id"] = id
    return event_dict

def add_app_context(
    logger: Bo<PERSON><PERSON>ogger,
    method_name: str,
    event_dict: EventDict,
) -> EventDict:
    """添加应用上下文到日志事件

    Args:
        logger: 日志记录器
        method_name: 方法名
        event_dict: 事件字典

    Returns:
        EventDict: 更新后的事件字典
    """
    event_dict["app_name"] = "gugu"
    return event_dict
