"""
用户模型转换器
"""
from typing import Optional
from app.models.rbac.user import UserModel
from app.schemas.rbac.user import User
from ..base import BaseConverter
from .role import RoleConverter
from ...schemas.rbac.role import Role


class UserConverter(BaseConverter[UserModel, User]):
    """用户模型转换器"""
    
    def __init__(self):
        """初始化转换器"""
        super().__init__()
        self.role_converter = RoleConverter()
    
    def to_schema(self, model: UserModel) -> User:
        """将用户模型转换为schema
        
        Args:
            model: 用户模型实例
            
        Returns:
            用户schema实例
        """
        # 保存原始roles



        original_roles = getattr(model, 'roles', [])
        roles = []
        for role_model in original_roles:
            roles.append(Role.model_validate({
                "id": str(role_model.id),
                "code": role_model.code,
                "name": role_model.name,
                "description": role_model.description,
                "parent_id": str(role_model.parent_id) if role_model.parent_id else None,
                "is_active": role_model.is_active,
                "is_system": role_model.is_system,
                "is_inherit": role_model.is_inherit,
                "sort_order": role_model.sort_order,
                "parent": None,  # 除非需要，否则设为None
                "children": [],  # 默认空列表
                "permissions": [],  # 默认空列表
                "permission_groups": [],  # 默认空列表
                "created_at": role_model.created_at,
                "updated_at": role_model.updated_at,
                "version": role_model.version if hasattr(role_model, "version") else "1.0.0"
            }))
        # 临时存储roles避免循环引用
        self._store_temp_attr(model, 'roles', [])

        # 转换基本属性
        user = User.model_validate(model)

        user.roles = roles
        # 恢复原始roles
        self._restore_temp_attr(model, 'roles')

        return user

        #
        # # 临时存储roles避免循环引用
        # self._store_temp_attr(model, 'roles', [])
        #
        # # 转换基本属性
        # user = User.model_validate(model)
        #
        # # 恢复
        # self._restore_temp_attr(model, 'roles')
        # return user
    
    def to_model(self, schema: User) -> UserModel:
        """将用户schema转换为模型
        
        Args:
            schema: 用户schema实例
            
        Returns:
            用户模型实例
        """
        # 转换基本属性
        user = UserModel()
        for field in schema.model_fields:
            if field != 'roles' and hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(user, field, value)
        
        # 转换roles
        if schema.roles:
            user.roles = [self.role_converter.to_model(role) for role in schema.roles]
        
        return user
