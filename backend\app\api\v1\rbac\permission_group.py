"""
权限组处理器
"""
import structlog
from typing import List
from tornado.web import HTT<PERSON><PERSON>rror
from dependency_injector.wiring import inject, Provide
from app.api.base import BaseHandler
from app.core.middleware import require_auth
from app.services.rbac.permission_group import PermissionGroupService
from app.core.di.containers import Container
from app.schemas.rbac.permission_group import (
    PermissionGroupCreateRequest,
    PermissionGroupUpdateRequest,
    PermissionGroupPermissionRequest
)

logger = structlog.get_logger(__name__)

class PermissionGroupHandler(BaseHandler):
    """权限组管理处理器"""
    
    def initialize(self, permission_group_service: PermissionGroupService = Provide[Container.permission_group_service]) -> None:
        """初始化处理器
        
        Args:
            permission_group_service: 权限组服务实例
        """
        super().initialize()
        self.permission_group_service = permission_group_service
    
    @require_auth(required=True, permissions=["permission_group:list"])
    async def get(self) -> None:
        """获取权限组列表"""
        try:
            # 获取查询参数
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))
            search = self.get_argument("search", None)
            
            # 调用服务获取权限组列表
            result = await self.permission_group_service.get_list(
                page=page,
                page_size=page_size,
                search=search
            )
            
            self.success_response(result.model_dump())
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("获取权限组列表失败", error=str(e))
            raise HTTPError(500, "获取权限组列表失败 " + str(e) )
    
    @require_auth(required=True, permissions=["permission_group:create"])
    async def post(self) -> None:
        """创建权限组"""
        try:
            # 验证请求数据
            data = PermissionGroupCreateRequest(**self.json_body)
            
            # 调用服务创建权限组
            result = await self.permission_group_service.create(data)
            self.success_response(result.model_dump())
        except ValueError as e:
            logger.error("创建权限组参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("创建权限组失败", error=str(e))
            raise HTTPError(500, "创建权限组失败 " + str(e))

class PermissionGroupDetailHandler(BaseHandler):
    """权限组详情处理器"""
    
    def initialize(self, permission_group_service: PermissionGroupService = Provide[Container.permission_group_service]) -> None:
        """初始化处理器
        
        Args:
            permission_group_service: 权限组服务实例
        """
        super().initialize()
        self.permission_group_service = permission_group_service
    
    @require_auth(required=True, permissions=["permission_group:detail"])
    async def get(self, group_id: str) -> None:
        """获取权限组详情
        
        Args:
            group_id: 权限组ID
        """
        try:
            # 调用服务获取权限组详情
            group = await self.permission_group_service.get_by_id(group_id)
            if not group:
                self.write_error(500, error_message="权限组不存在")
            self.success_response(group.model_dump())
        except ValueError as e:
            logger.error("创建权限组参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("获取权限组详情失败", error=str(e))
            raise HTTPError(500, "获取权限组详情失败 " + str(e))
    
    @require_auth(required=True, permissions=["permission_group:update"])
    async def put(self, group_id: str) -> None:
        """更新权限组
        
        Args:
            group_id: 权限组ID
        """
        try:
            # 验证请求数据
            data = PermissionGroupUpdateRequest(**self.json_body)
            data.permission_group_id = group_id
            
            # 调用服务更新权限组
            result = await self.permission_group_service.update(data)
            if not result:
                self.write_error(500, "权限组不存在")
            self.success_response(result.model_dump())
        except ValueError as e:
            logger.error("更新权限组参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("更新权限组失败", error=str(e))
            raise HTTPError(500, "更新权限组失败")
    
    @require_auth(required=True, permissions=["permission_group:delete"])
    async def delete(self, group_id: str) -> None:
        """删除权限组
        
        Args:
            group_id: 权限组ID
        """
        try:
            # 调用服务删除权限组
            error = await self.permission_group_service.delete(group_id)
            if error:
                self.write_error(500, error)
            self.success_response()
        except ValueError as e:
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("删除权限组失败", error=str(e))
            self.write_error(500, error_message="删除权限组失败" +str(e))

    @require_auth(required=True, permissions=["permission_group:add_permission"])
    async def post(self, group_id: str) -> None:
        """添加权限到权限组
        
        Args:
            group_id: 权限组ID
        """
        try:
            # 验证请求数据
            data = PermissionGroupPermissionRequest(**self.json_body)
            data.permission_group_id = group_id
            
            # 调用服务添加权限
            result = await self.permission_group_service.add_permissions(data)
            if not result:
                raise HTTPError(404, "权限组不存在")
            
            self.success_response(result.model_dump())
        except ValueError as e:
            logger.error("添加权限参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("添加权限失败", error=str(e))
            raise HTTPError(500, "添加权限失败 " + str(e))
    
    @require_auth(required=True, permissions=["permission_group:remove_permission"])
    async def patch(self, group_id: str) -> None:
        """从权限组移除权限
        
        Args:
            group_id: 权限组ID
        """
        try:
            # 验证请求数据
            data = PermissionGroupPermissionRequest(**self.json_body)
            data.permission_group_id = group_id
            
            # 调用服务移除权限
            result = await self.permission_group_service.remove_permissions(data)
            if not result:
                self.write_error(500, error_message="权限组不存在")
            
            self.success_response(result.model_dump())
        except ValueError as e:
            logger.error("移除权限参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("移除权限失败", error=str(e))
            raise HTTPError(500, "移除权限失败")
