"""
GitHub项目数据库模型
"""
from datetime import datetime
import uuid
from sqlalchemy import Column, String, Text, DateTime, Foreign<PERSON>ey, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.dialects.postgresql import JSON
from app.models.model_base import ModelBase
from app.utils.status_enum import ProjectStatusEnum


class GitHubProjectModel(ModelBase):
    """GitHub项目表"""
    __tablename__ = "github_projects"


    name = Column(String(255), nullable=False)
    repository_url = Column(String(2048), nullable=False, unique=True)
    description_recommend = Column(Text, nullable=True)
    description_project = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True, default=list)
    status = Column(String(50), nullable=False, default=ProjectStatusEnum.DRAFT.value)
    project_phase = Column(String(50), nullable=False, default=ProjectStatusEnum.NOT_DOWNLOAD.value)
    stars = Column(String(64), nullable=True)
    background_color = Column(String(8), nullable=True)
    button_color = Column(String(8), nullable=True)
    local_path = Column(String(1024), nullable=True)
    image_url = Column(String(1024), nullable=True)
    image_list = Column(Text, nullable=True)
    icon_url = Column(String(1024), nullable=True)
    architecture_mermaid = Column(Text, nullable=True)
    dependency_mermaid = Column(Text, nullable=True)
    shared_count_qrcode = Column(String(8), nullable=True, default="0")
    shared_count_link = Column(String(8), nullable=True, default="0")
    shared_pic = Column(Text, nullable=True)
    shared_data = Column(Text, nullable=True)
    is_priority = Column(Boolean, nullable=True, default=False)
    collect_count = Column(Integer, nullable=True, default=0)
    read_count = Column(Integer, nullable=True, default=0)


    def __repr__(self):
        return f"<GitHubProject {self.name}>" 