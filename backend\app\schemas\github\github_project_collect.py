#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/28 15:42
# @File    : github_project_collect.py
# @Description: 
"""
"""
用户收藏项目相关的Schema定义
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.schemas.github.github_project import GitHubProject


class UserProjectCollectCreate(BaseModel):
    """创建用户收藏的请求Schema（支持项目和文章）"""
    project_id: Optional[str] = Field(None, description="项目ID")
    article_id: Optional[str] = Field(None, description="文章ID")
    
    @validator('article_id')
    def validate_ids(cls, v, values):
        """验证project_id和article_id不能同时为空或同时有值"""
        project_id = values.get('project_id')
        
        # 两个都为空
        if not project_id and not v:
            raise ValueError('project_id 和 article_id 不能同时为空')
        
        # 两个都有值
        if project_id and v:
            raise ValueError('project_id 和 article_id 不能同时提供')
            
        return v


class UserProjectCollectResponse(BaseModel):
    """用户收藏项目响应Schema"""
    id: str = Field(..., description="收藏记录ID")
    user_id: str = Field(..., description="用户ID")
    project_id: Optional[str] = Field(None, description="项目ID")
    article_id: Optional[str] = Field(None, description="文章ID")
    created_at: datetime = Field(..., description="收藏时间")
    return_type: Optional[str] = Field(None, description="文章类型")

    # 包含项目详细信息
    project: Optional[GitHubProject] = Field(None, description="项目详情")
    # 包含文章详细信息
    article: Optional[dict] = Field(None, description="文章详情")

    class Config:
        from_attributes = True


class UserProjectCollectList(BaseModel):
    """用户收藏项目列表响应Schema"""
    collects: List[UserProjectCollectResponse] = Field(..., description="收藏列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    page_size: int = Field(..., description="每页大小")