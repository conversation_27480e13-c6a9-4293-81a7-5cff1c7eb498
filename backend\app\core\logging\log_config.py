"""Logging configuration module."""
import logging
import logging.config
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional

import structlog
from structlog.types import Processor, EventDict
from structlog.stdlib import ProcessorFormatter, BoundLogger

from ..config import settings
from .filters import SensitiveDataFilter
from .context import add_request_id, add_app_context

def setup_logging(
    default_level: Optional[str] = None,
    logs_dir: Optional[str] = None,
    config_path: Optional[str] = None
) -> None:
    """
    配置结构化日志系统
    
    Args:
        default_level: 默认日志级别，如果为None则使用配置中的值
        logs_dir: 日志文件目录，如果为None则使用配置中的值
        config_path: 日志配置文件路径（暂未使用）
    """
    # 使用配置值或默认值
    default_level = default_level or settings.app.LOGGING.LEVEL
    logs_dir = logs_dir or settings.app.LOGGING.DIRECTORY
    max_bytes = settings.app.LOGGING.MAX_FILE_SIZE
    backup_count = settings.app.LOGGING.BACKUP_COUNT
    error_backup_count = settings.app.LOGGING.ERROR_BACKUP_COUNT
    
    # 创建日志目录
    os.makedirs(logs_dir, exist_ok=True)
    
    # 生成日志文件名
    current_date = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(logs_dir, f'app-{current_date}.log')
    error_log_file = os.path.join(logs_dir, f'error-{current_date}.log')
    plain_log_file = os.path.join(logs_dir, f'plain-{current_date}.log')
    
    # 配置结构化日志处理器
    shared_processors: list[Processor] = [
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.add_log_level,
        structlog.stdlib.add_logger_name,
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        add_app_context,
        add_request_id,
    ]
    
    # 配置structlog
    structlog.configure(
        processors=[
            *shared_processors,
            structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # 配置标准库日志
    logging.config.dictConfig({
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'json': {
                '()': ProcessorFormatter,
                'processor': structlog.processors.JSONRenderer(ensure_ascii=False),
                'foreign_pre_chain': shared_processors,
            },
            'console': {
                '()': ProcessorFormatter,
                'processor': structlog.dev.ConsoleRenderer(colors=True),
                'foreign_pre_chain': shared_processors,
            },
            'plain': {
                'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'filters': {
            'sensitive_data': {
                '()': SensitiveDataFilter,
            }
        },
        'handlers': {
            'console': {
                'level': logging.DEBUG if settings.app.DEBUG else logging.INFO,
                'class': 'logging.StreamHandler',
                'formatter': 'console',
                'filters': ['sensitive_data'],
                'stream': sys.stdout,
            },
            'file': {
                'level': logging.INFO,
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': log_file,
                'maxBytes': max_bytes,
                'backupCount': backup_count,
                'formatter': 'json',
                'filters': ['sensitive_data'],
                'encoding': 'utf8',
            },
            'error_file': {
                'level': logging.ERROR,
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': error_log_file,
                'maxBytes': max_bytes,
                'backupCount': error_backup_count,
                'formatter': 'json',
                'filters': ['sensitive_data'],
                'encoding': 'utf8',
            },
            'plain_file': {
                'level': logging.INFO,
                'class': 'logging.handlers.RotatingFileHandler',
                'filename': plain_log_file,
                'maxBytes': max_bytes,
                'backupCount': backup_count,
                'formatter': 'plain',
                'filters': ['sensitive_data'],
                'encoding': 'utf8',
            }
        },
        'loggers': {
            '': {  # root logger
                'handlers': ['console', 'file', 'error_file', 'plain_file'],
                'level': getattr(logging, default_level.upper()),
                'propagate': True
            },
            'tornado.access': {
                'handlers': ['file', 'plain_file'],
                'level': logging.INFO,
                'propagate': False
            },
            'tornado.application': {
                'handlers': ['file', 'error_file', 'plain_file'],
                'level': logging.INFO,
                'propagate': False
            },
            'tornado.general': {
                'handlers': ['file', 'plain_file'],
                'level': logging.INFO,
                'propagate': False
            }
        }
    })
