#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文章收藏API处理器
"""
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.schemas.article.article_collect import UserArticleCollectCreate
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.services.article.article_service import ArticleService
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)


class ArticleCollectHandler(BaseHandler):
    """文章收藏处理器"""

    @inject
    def initialize(
        self,
        article_collect_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            article_collect_service: 文章收藏服务
        """
        super().initialize()
        self.article_collect_service = article_collect_service

    @require_auth(required=True)
    async def get(self):
        """获取用户收藏的文章列表

        查询参数:
        - page: 页码，默认1
        - page_size: 每页大小，默认10
        - search: 搜索关键词，可选
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 获取分页参数
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "10")
            search = self.get_argument("search", "").strip() or None

            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 10
            except ValueError:
                page_size = 10

            # 获取用户收藏列表
            result = await self.article_collect_service.get_user_collects(
                user_id=user_id,
                search=search,
                page=page,
                page_size=page_size
            )

            # 返回结果
            self.success_response({
                "collects": [collect.dict() for collect in result.collects],
                "total": result.total,
                "page": result.page,
                "page_size": result.page_size
            })

        except ValueError as e:
            logger.error("获取文章收藏列表参数错误", error=str(e), user_id=user_id)
            self.write_error(500, error_message=f"请求参数错误: {str(e)}")
        except Exception as e:
            logger.error("获取文章收藏列表失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"获取收藏列表失败: {str(e)}")

    @require_auth(required=True)
    async def post(self):
        """收藏文章

        请求体:
        {
            "article_id": "文章ID"
        }
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 验证请求数据
            if not self.json_body:
                raise ValueError("请求体不能为空")

            collect_data = UserArticleCollectCreate.model_validate(self.json_body)

            # 添加收藏
            result = await self.article_collect_service.add_collect(
                user_id=user_id,
                collect_data=collect_data
            )

            # 返回结果
            self.success_response(result.dict(), message="收藏成功")

        except ValueError as e:
            logger.error("收藏文章参数错误", error=str(e), user_id=user_id)
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("收藏文章失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"收藏文章失败: {str(e)}")

    @require_auth(required=True)
    async def delete(self):
        """取消收藏文章

        查询参数:
        - article_id: 文章ID
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 获取文章ID
            article_id = self.get_argument("article_id", None)
            if not article_id:
                raise ValueError("文章ID不能为空")

            # 取消收藏
            await self.article_collect_service.remove_collect(
                user_id=user_id,
                article_id=article_id
            )

            # 返回结果
            self.success_response({}, message="取消收藏成功")

        except ValueError as e:
            logger.error("取消收藏文章参数错误", error=str(e), user_id=user_id)
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("取消收藏文章失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"取消收藏失败: {str(e)}")


class ArticleCollectCheckHandler(BaseHandler):
    """检查文章收藏状态处理器"""

    @inject
    def initialize(
        self,
        article_collect_service:ArticleService  = Provide[Container.article_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.article_collect_service = article_collect_service

    @require_auth(required=True)
    async def get(self):
        """检查文章是否被收藏

        查询参数:
        - article_id: 文章ID
        """
        try:
            # 获取当前用户ID
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            # 获取文章ID
            article_id = self.get_argument("article_id", None)
            if not article_id:
                raise ValueError("文章ID不能为空")

            # 检查是否收藏
            is_collected = await self.article_collect_service.check_is_collected(
                user_id=user_id,
                article_id=article_id
            )

            # 返回结果
            self.success_response({
                "is_collected": is_collected,
                "article_id": article_id
            })

        except ValueError as e:
            logger.error("检查文章收藏状态参数错误", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("检查文章收藏状态失败", error=str(e), exc_info=True)
            self.write_error(500, error_message=f"检查收藏状态失败: {str(e)}") 