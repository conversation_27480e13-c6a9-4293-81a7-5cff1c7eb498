#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GitHub项目关注/订阅数据库模型
"""
from datetime import datetime
from sqlalchemy import Column, String, DateTime, ForeignKey, Boolean, Text, Index
from sqlalchemy.orm import relationship

from app.models.model_base import ModelBase


class GitHubProjectSubscriptionModel(ModelBase):
    """GitHub项目关注/订阅表"""

    __tablename__ = "github_project_subscriptions"
    project_id = Column(String(36), ForeignKey("github_projects.id"), nullable=False, comment="项目ID")

    def __repr__(self):
        return f"<GitHubProjectSubscription project_id={self.project_id}"
