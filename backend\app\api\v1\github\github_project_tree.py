"""
GitHub项目文件树API处理器
"""
import json
import os
import structlog
from typing import List, Dict, Any, Optional
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.services.github.github_project import GitHubProjectService
from app.services.git import GitService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)

class GitHubProjectTreeHandler(BaseHandler):
    """GitHub项目文件树处理器"""
    
    @inject
    def initialize(
        self,
        github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
    
    # @require_auth(required=True, permissions=["github:project:tree"])
    async def get(self) -> None:
        """获取GitHub项目文件树
        
        Args:
            project_id: 项目ID
        """
        try:
            # 获取查询参数
            depth = int(self.get_argument("depth", "1"))
            expand_paths = self.get_argument("expand_paths", "")
            project_id: str = self.get_argument("project_id", "")
            user_id = get_current_user_id(auth_header=self.request.headers.get('Authorization'))

            # 将expand_paths解析为列表
            expand_paths_list = [p.strip() for p in expand_paths.split(",") if p.strip()]
            
            # 获取项目详情
            project = await self.github_project_service.get_project(project_id, user_id)
            
            if not project:
                return self.write_error(500, error_message=f"项目不存在")
            
            # 检查本地路径是否存在
            local_path = project.local_path

            project_dict = project.dict()
            if project_dict.get('shared_data'):
                try:
                    shared_data = json.loads(project_dict['shared_data'])
                    # 确保shared_data是list类型
                    if not isinstance(shared_data, list):
                        shared_data = [shared_data]
                    project_dict['shared_data'] = shared_data
                except json.JSONDecodeError:
                    project_dict['shared_data'] = []

            if not local_path or not os.path.exists(local_path):
                logger.warning(f"项目本地路径不存在，使用测试树: {project_id}")
                tree = await self.get_test_tree(depth, expand_paths)
            else:
                # 使用真实文件树
                tree = await self.get_file_tree(depth, expand_paths, local_path)

            # 返回结果
            self.success_response({
                "project": project_dict,
                "tree": tree
            })
            
        except ValueError as e:
            logger.error("获取项目文件树参数错误", error=str(e))
            self.write_error(500, error_message=f"获取项目文件树参数错误:"  + str(e))
        except Exception as e:
            logger.error("获取项目文件树时发生错误", error=str(e), project_id=project_id)
            self.write_error(500, error_message=f"获取项目文件树时发生错误:"  + str(e))
    
    async def get_file_tree(self, max_depth: int, expand_paths: List[str], local_path: str = "", current_path: str = "", current_depth: int = 0) -> List[Dict[str, Any]]:
        """递归获取文件树结构
        
        Args:
            max_depth: 最大递归深度
            expand_paths: 需要深度展开的路径列表
            local_path: 项目在本地的根目录路径
            current_path: 当前处理的相对路径
            current_depth: 当前递归深度
            
        Returns:
            List[Dict[str, Any]]: 文件树结构列表
        """
        result = []
        
        # 确定当前目录的绝对路径
        abs_path = os.path.join(local_path, current_path)
        
        # 检查路径是否存在
        if not os.path.exists(abs_path):
            logger.error(f"路径不存在: {abs_path}")
            return result
        
        # 遍历目录内容
        try:
            entries = os.listdir(abs_path)
            
            # 对条目进行排序（目录在前，文件在后）
            entries.sort(key=lambda x: (not os.path.isdir(os.path.join(abs_path, x)), x.lower()))
            
            for entry in entries:
                entry_path = os.path.join(abs_path, entry)
                rel_path = os.path.join(current_path, entry) if current_path else entry
                
                # 跳过隐藏文件和特殊目录
                if entry.startswith('.') and entry != '.gitignore':
                    continue
                    
                # 基本信息
                entry_info = {
                    "name": entry,
                    "path": rel_path,
                    "type": "dir" if os.path.isdir(entry_path) else "file"
                }
                
                # 如果是文件，添加大小信息
                if os.path.isfile(entry_path):
                    entry_info["size"] = os.path.getsize(entry_path)
                
                # 如果是目录，并且（未达到最大深度 或 在展开路径列表中），则递归处理
                if os.path.isdir(entry_path):
                    should_expand = (current_depth < max_depth) or any(
                        rel_path == expand_path or rel_path.startswith(f"{expand_path}/") or expand_path.startswith(f"{rel_path}/")
                        for expand_path in expand_paths
                    )
                    
                    if should_expand:
                        children = await self.get_file_tree(
                            max_depth,
                            expand_paths,
                            local_path,
                            rel_path,
                            current_depth + 1
                        )
                        entry_info["children"] = children
                    else:
                        # 标记为有子项但未展开
                        entry_info["has_children"] = len(os.listdir(entry_path)) > 0
                
                result.append(entry_info)
            
            return result
        
        except Exception as e:
            logger.error(f"获取文件树时出错", path=abs_path, error=str(e))
            return result

    async def get_test_tree(self, max_depth: int, expand_paths: List[str], local_path: str = "", current_path: str = "", current_depth: int = 0) -> List[Dict[str, Any]]:
        """生成模拟文件树结构，用于测试，参数与get_file_tree完全一致
        
        Args:
            max_depth: 最大递归深度
            expand_paths: 需要深度展开的路径列表
            local_path: 项目在本地的根目录路径 (保持参数一致性，但在此函数中不使用)
            current_path: 当前处理的相对路径 (仅在递归时使用)
            current_depth: 当前递归深度 (仅在递归时使用)
            
        Returns:
            List[Dict[str, Any]]: 模拟的文件树结构列表
        """
        # 模拟的项目结构
        mock_structure = {
            "src": {
                "type": "dir",
                "children": {
                    "main.py": {"type": "file", "size": 2048},
                    "utils": {
                        "type": "dir",
                        "children": {
                            "helpers.py": {"type": "file", "size": 1024},
                            "validators.py": {"type": "file", "size": 1536}
                        }
                    },
                    "models": {
                        "type": "dir",
                        "children": {
                            "user.py": {"type": "file", "size": 3072},
                            "product.py": {"type": "file", "size": 2560}
                        }
                    }
                }
            },
            "tests": {
                "type": "dir",
                "children": {
                    "test_main.py": {"type": "file", "size": 1280},
                    "test_models.py": {"type": "file", "size": 1792}
                }
            },
            "docs": {
                "type": "dir",
                "children": {
                    "api.md": {"type": "file", "size": 4096}
                }
            },
            ".gitignore": {"type": "file", "size": 256},
            "README.md": {"type": "file", "size": 1024}
        }
        
        # 如果是递归调用并且有current_path，则获取当前路径对应的子结构
        if current_path:
            # 拆分路径获取每个部分
            parts = current_path.split(os.path.sep)
            current_struct = mock_structure
            
            # 逐层查找
            for part in parts:
                if not part:
                    continue
                if part in current_struct and current_struct[part]["type"] == "dir" and "children" in current_struct[part]:
                    current_struct = current_struct[part]["children"]
                else:
                    # 如果路径不存在，返回空列表
                    return []
        else:
            current_struct = mock_structure
        
        result = []
        
        # 构建当前层级的树结构
        for name, info in current_struct.items():
            if name.startswith('.') and name != '.gitignore':
                continue
            
            path = os.path.join(current_path, name) if current_path else name
            
            if info["type"] == "file":
                result.append({
                    "name": name,
                    "path": path,
                    "type": "file",
                    "size": info.get("size", 0)
                })
            else:  # dir
                dir_info = {
                    "name": name,
                    "path": path,
                    "type": "dir"
                }
                
                # 判断是否需要展开子目录
                should_expand = (current_depth < max_depth) or any(
                    path == expand_path or path.startswith(f"{expand_path}/") or 
                    expand_path.startswith(f"{path}/") for expand_path in expand_paths
                )
                
                if should_expand and "children" in info:
                    children = await self.get_test_tree(
                        max_depth,
                        expand_paths,
                        local_path,
                        path,
                        current_depth + 1
                    )
                    dir_info["children"] = children
                elif "children" in info:
                    # 标记有子项但未展开
                    dir_info["has_children"] = len(info["children"]) > 0
                
                result.append(dir_info)
        
        # 对结果排序：目录在前，文件在后
        result.sort(key=lambda x: (x["type"] != "dir", x["name"].lower()))
        return result