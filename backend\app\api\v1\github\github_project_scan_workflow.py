#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/17 15:37
# @File    : github_project_scan_workflow.py
# @Description: 
"""


from typing import Dict, Any, Optional, List
import structlog
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.schemas.github.github_project import GitHubProjectCreate, GitHubProject
from app.services.article.article_service import ArticleService
from app.services.github.github_downloader import GitHubDownloader
from app.services.github.github_project import GitHubProjectService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.utils.flow_log_utils import FlowLogType
from app.utils.project_scanner_utils import ProjectScannerUtils
from app.utils.security import get_current_user_id
from app.utils.status_enum import ProjectStatusEnum

logger = structlog.get_logger(__name__)

# 人工筛选并投放项目的具体过程
# 1.扫描 get
# 2.筛选下载 post
# 3.获取项目下载进度 get
# 4.处理下载状态 post
# 5.获取项目分析进度 get
# 6.处理项目分析状态 post


class ProjectScanHandler(BaseHandler):
    """GitHub/Gitee项目扫描处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:list"])
    async def get(self) -> None:
        """获取项目列表"""
        try:
            # 获取请求来源参数
            source = self.get_argument("source", "github")  # github 或 gitee

            # 获取基础搜索参数
            page = int(self.get_argument("page", "1"))
            language = self.get_argument("language", None)
            keyword = self.get_argument("keyword", None)
            sort = self.get_argument("sort", "stars")
            order = self.get_argument("order", "desc")
            page_size = self.get_argument("page_size", "30")

            # 获取范围筛选参数
            stars_min = self.get_argument("stars_min", None)
            stars_max = self.get_argument("stars_max", None)
            size_min = self.get_argument("size_min", None)
            size_max = self.get_argument("size_max", None)

            # 转换数字参数
            if stars_min:
                stars_min = int(stars_min)
            if stars_max:
                stars_max = int(stars_max)
            if size_min:
                size_min = int(size_min)
            if size_max:
                size_max = int(size_max)
            if page and page > 998:
                raise ValueError("最多获取前1000页")


            # 根据来源调用不同的搜索方法
            if source.lower() == "github":
                result = await ProjectScannerUtils.search_github_projects(
                    page=page,
                    language=language,
                    stars_min=stars_min,
                    stars_max=stars_max,
                    size_min=size_min,
                    size_max=size_max,
                    keyword=keyword,
                    sort=sort,
                    order=order,
                    page_size=page_size
                )
            elif source.lower() == "gitee":
                result = await ProjectScannerUtils.search_gitee_projects(
                    page=page,
                    language=language,
                    stars_min=stars_min,
                    stars_max=stars_max,
                    size_min=size_min,
                    size_max=size_max,
                    keyword=keyword,
                    sort=sort,
                    order=order,
                    page_size=page_size

                )
            else:
                self.write_error(400, error_message=f"不支持的搜索来源: {source}")
                return

            self.success_response(result)

        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(400, error_message="请求参数无效: " + str(e))
        except Exception as e:
            logger.error("获取项目列表时发生错误", error=str(e))
            self.write_error(500, error_message="获取项目扫描列表时发生错误: " + str(e))

class ProjectLinkCheckHandler(BaseHandler):
    """项目链接检查处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def post(self) -> None:
        """
            处理项目下载状态
        """
        try:
            github_links = self.json_body.get("github_links", [])
            # 调用服务层处理项目状态
            result = await self.github_project_service.link_check_solver(github_links)
            self.success_response(result)

        except Exception as e:
            logger.error("项目链接检查时发生错误", error=str(e))
            self.write_error(500, error_message="项目链接检查时发生错误: " + str(e))

class ProjectDownloadHandler(BaseHandler):
    """项目下载状态处理处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):

        self.github_project_service = github_project_service
        super().initialize()

    async def post(self) -> None:
        try:
            # 谁创建项目
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            # 创建什么项目
            projects_data = self.json_body.get("projects", [])
            if not projects_data:
                raise ValueError("projects 列表不能为空")
            projects = [GitHubProjectCreate(**project) for project in projects_data]
            result = await self.github_project_service.download_projects(projects, user_id)

            processed_result = []
            for item in result:
                processed_item = {
                    "success": item["success"],
                    "error": item["error"]
                }
                # 如果 project 存在且不是 None，转换为字典
                if item["project"] is not None:
                    processed_item["project"] = item["project"].dict()
                else:
                    processed_item["project"] = None

                processed_result.append(processed_item)

            # 返回结果
            self.success_response({
                "github_projects": processed_result,
            })


        except Exception as e:
            logger.error("处理项目下载状态时发生错误", error=str(e))
            self.write_error(500, error_message=str(e))

class ProjectPriorityHandler(BaseHandler):
    """项目加急处理处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):

        self.github_project_service = github_project_service
        super().initialize()


    async def post(self) -> None:
        """
        项目加急
        """
        try:
            project_id = self.json_body.get("project_id", None)
            # 调用服务层处理项目状态
            result = await self.github_project_service.solve_priority(project_id)
            self.success_response(result)

        except Exception as e:
            logger.error("处理项目下载状态时发生错误", error=str(e))
            self.write_error(500, error_message="处理项目下载状态时发生错误: " + str(e))

class ProjectCancelGenerationHandler(BaseHandler):
    """项目下载状态处理处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):

        self.github_project_service = github_project_service
        super().initialize()


    async def post(self) -> None:
        """
        处理项目下载状态
        """
        try:
            project_id = self.json_body.get("project_id")
            # 调用服务层处理项目状态
            result = await self.github_project_service.cancel_project([project_id])
            self.success_response(result)

        except Exception as e:
            logger.error("处理项目下载状态时发生错误", error=str(e))
            self.write_error(500, error_message="处理项目下载状态时发生错误: " + str(e))


class ProjectGetFlowStatusHandler(BaseHandler):
    """获取下载-分析状态进度处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:progress"])
    async def get(self) -> None:
        """获取项目状态统计和分页列表

        支持分页参数：
        - page: 页码（默认1）
        - page_size: 每页数量（默认10，最大100）
        - project_phase: 按项目阶段筛选（可选）
        """
        try:
            # 获取分页参数
            project_phase = self.get_argument("project_phase", None)

            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "10")

            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 30
            except ValueError:
                page_size = 30

            # 获取分页数据和状态统计
            result = await self.github_project_service.get_project_status_with_pagination(
                page=page,
                page_size=page_size,
                project_phase=project_phase
            )

            self.success_response(result)

        except ValueError as e:
            logger.error("分页参数无效", error=str(e))
            self.write_error(400, error_message="分页参数无效: " + str(e))
        except Exception as e:
            logger.error("获取项目状态统计时发生错误", error=str(e))
            self.write_error(500, error_message="获取项目状态统计时发生错误: " + str(e))


class ProjectGetFlowStatusCurrentUserHandler(BaseHandler):
    """获取当前用户的 下载-分析状态进度处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:progress"])
    async def get(self) -> None:
        """获取当前用户的 项目状态统计和分页列表

        支持分页参数：
        - page: 页码（默认1）
        - page_size: 每页数量（默认10，最大100）
        - project_phase: 按项目阶段筛选（可选）
        """
        try:
            # 获取分页参数
            project_phase = self.get_argument("project_phase", None)

            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "10")

            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 30
            except ValueError:
                page_size = 30

            user_id = get_current_user_id(auth_header=self.request.headers.get('Authorization'))

            # 获取分页数据和状态统计
            result = await self.github_project_service.get_project_status_with_pagination(
                page=page,
                page_size=page_size,
                project_phase=project_phase,
                created_by=user_id
            )

            self.success_response(result)

        except ValueError as e:
            logger.error("分页参数无效", error=str(e))
            self.write_error(400, error_message="分页参数无效: " + str(e))
        except Exception as e:
            logger.error("获取项目状态统计时发生错误", error=str(e))
            self.write_error(500, error_message="获取项目状态统计时发生错误: " + str(e))



# 流程日志
class ProjectUserFlowLog(BaseHandler):
    """用户项目日志"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:list"])
    async def get(self) -> None:
        """获取日志列表"""
        try:
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "30")
            name = self.get_argument("name", None)
            log_type = self.get_argument("log_type", None)


            # 确保 page 和 page_size 是有效的整数
            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 30
            except ValueError:
                page_size = 30

            # 处理 name 参数，如果是空字符串则设为 None
            if name is not None and name.strip() == "":
                name = None
            if log_type is not None and log_type.strip() == "":
                log_type = None

            result = await self.github_project_service.get_user_flow_logs(page, page_size, name)
            self.success_response(result)

        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(400, error_message="请求参数无效: " + str(e))
        except Exception as e:
            logger.error("获取项目列表时发生错误", error=str(e))
            self.write_error(500, error_message="获取获取日志列表时发生错误: " + str(e))


# 流程日志
class ProjectUserHistoryLog(BaseHandler):
    """用户项目日志"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service],
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        self.article_service = article_service
        super().initialize()

    # @require_auth(required=True, permissions=["github:project:list"])
    async def get(self) -> None:
        """获取日志列表"""
        try:
            page_str = self.get_argument("page", "1")
            page_size_str = self.get_argument("page_size", "30")
            name = self.get_argument("name", None)
            log_type = self.get_argument("log_type", None)

            # 确保 page 和 page_size 是有效的整数
            try:
                page = int(page_str) if page_str.strip() else 1
            except ValueError:
                page = 1

            try:
                page_size = int(page_size_str) if page_size_str.strip() else 30
            except ValueError:
                page_size = 30
            user_id = get_current_user_id(auth_header=self.request.headers.get('Authorization'))

            if log_type == FlowLogType.ARTICLE_HISTORY:
                result = await self.article_service.get_user_article_logs_by_log_type(page, page_size, name, log_type,
                                                                                     user_id=user_id)
            else:
                result = await self.github_project_service.get_user_logs_by_log_type(page, page_size, name, log_type, user_id=user_id)
            # result = await self.github_project_service.get_history_logs(page, page_size, name, user_id=user_id)

            self.success_response(result)

        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(400, error_message="请求参数无效: " + str(e))
        except Exception as e:
            logger.error("获取项目列表时发生错误", error=str(e))
            self.write_error(500, error_message="获取获取日志列表时发生错误: " + str(e))

