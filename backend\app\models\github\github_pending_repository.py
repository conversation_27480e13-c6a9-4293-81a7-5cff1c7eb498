"""
GitHub待处理仓库数据库模型

用于存储通过URL验证有效但尚未被索引的GitHub仓库信息
"""
from datetime import datetime, timezone
from collections import Counter
from sqlalchemy import String, Text, JSON, ForeignKey
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.orm import Mapped, mapped_column, relationship
from typing import List
import enum

from app.models.model_base import ModelBase


class SubmissionSourceType(str, enum.Enum):
    """提交来源类型枚举"""
    URL_VALIDATION = "url_validation"  # URL验证
    API_SUBMISSION = "api_submission"  # API提交
    BATCH_IMPORT = "batch_import"  # 批量导入
    USER_REQUEST = "user_request"  # 用户请求
    ADMIN_ADD = "admin_add"  # 管理员添加
    OTHER = "other"  # 其他


class GitHubRepositorySubmissionModel(ModelBase):
    """GitHub仓库提交来源记录表

    独立记录每次仓库提交的来源信息，支持一个仓库多次提交的场景
    """
    __tablename__ = "github_repository_submissions"

    # 关联的仓库ID（外键）
    repository_id: Mapped[str] = mapped_column(
        String(64),
        ForeignKey('github_pending_repositories.id', ondelete='CASCADE'),
        nullable=False,
        comment='关联的GitHub待处理仓库ID'
    )

    # 提交来源信息
    source_type: Mapped[SubmissionSourceType] = mapped_column(
        String(50),
        nullable=False,
        default=SubmissionSourceType.URL_VALIDATION,
        comment='提交来源类型'
    )
    submitted_by_ip: Mapped[str] = mapped_column(String(45), nullable=True, comment='提交者IP地址')
    submitted_by_user: Mapped[str] = mapped_column(Text, nullable=True, comment='提交者用户名')
    submitted_by_user_agent: Mapped[str] = mapped_column(Text, nullable=True, comment='提交者User Agent')
    submission_metadata: Mapped[dict] = mapped_column(
        MutableDict.as_mutable(JSON),
        nullable=True,
        comment='提交上下文信息(JSON格式)'
    )

    # 关系定义
    repository: Mapped["GitHubPendingRepositoryModel"] = relationship(
        "GitHubPendingRepositoryModel",
        back_populates="submissions"
    )

    def __repr__(self):
        return f"<GitHubRepositorySubmission {self.source_type} for {self.repository_id}>"

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": str(self.id),
            "repository_id": str(self.repository_id),
            "source_type": self.source_type,
            "submitted_by_ip": self.submitted_by_ip,
            "submitted_by_user": self.submitted_by_user,
            "submitted_by_user_agent": self.submitted_by_user_agent,
            "submission_metadata": self.submission_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }


class GitHubPendingRepositoryModel(ModelBase):
    """GitHub待处理仓库表

    存储通过URL验证有效但尚未被索引到搜索引擎中的仓库信息
    """
    __tablename__ = "github_pending_repositories"

    # 基本仓库信息
    repository_url: Mapped[str] = mapped_column(String(2048), nullable=False, unique=True, comment="仓库URL")
    platform: Mapped[str] = mapped_column(String(50), nullable=False, comment="代码托管平台(github, gitlab, gitee等)")
    username: Mapped[str] = mapped_column(String(255), nullable=False, comment="用户名或组织名")
    repository_name: Mapped[str] = mapped_column(String(255), nullable=False, comment="仓库名称")

    # 验证状态
    validation_status: Mapped[str] = mapped_column(String(50), nullable=False, default="pending", comment="验证状态: pending, validated, failed")
    validation_message: Mapped[str] = mapped_column(Text, nullable=True, comment="验证消息或错误信息")
    last_validation_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc), comment="最后验证时间")

    # 处理状态
    processing_status: Mapped[str] = mapped_column(String(50), nullable=False, default="pending", comment="处理状态: pending, processing, completed, failed")
    processing_message: Mapped[str] = mapped_column(Text, nullable=True, comment="处理消息或错误信息")
    last_processing_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=True, comment="最后处理时间")

    # 关系定义 - 一对多关系到提交来源记录
    submissions: Mapped[List["GitHubRepositorySubmissionModel"]] = relationship(
        "GitHubRepositorySubmissionModel",
        back_populates="repository",
        cascade="all, delete-orphan",
        order_by="GitHubRepositorySubmissionModel.created_at.desc()"
    )
    
    def __repr__(self):
        return f"<GitHubPendingRepository {self.username}/{self.repository_name}>"

    def to_dict(self, include_submissions=False):
        """转换为字典格式

        Args:
            include_submissions: 是否包含提交来源记录列表
        """
        result = {
            "id": str(self.id),
            "repository_url": self.repository_url,
            "platform": self.platform,
            "username": self.username,
            "repository_name": self.repository_name,
            "validation_status": self.validation_status,
            "validation_message": self.validation_message,
            "last_validation_at": self.last_validation_at.isoformat() if self.last_validation_at else None,
            "processing_status": self.processing_status,
            "processing_message": self.processing_message,
            "last_processing_at": self.last_processing_at.isoformat() if self.last_processing_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

        if include_submissions:
            result["submissions"] = [submission.to_dict() for submission in self.submissions]
            result["submission_count"] = len(self.submissions)
            result["submission_sources_summary"] = self.get_submission_sources_summary()

        return result

    def get_submission_sources_summary(self):
        """获取提交来源统计摘要

        Returns:
            dict: 按来源类型分组的统计信息
        """
        source_counts = Counter(submission.source_type for submission in self.submissions)
        return dict(source_counts)

    def get_submission_count(self):
        """获取提交次数总计

        Returns:
            int: 提交次数
        """
        return len(self.submissions)

    def get_latest_submission(self):
        """获取最新的提交记录

        Returns:
            GitHubRepositorySubmissionModel: 最新的提交记录，如果没有则返回None
        """
        return self.submissions[0] if self.submissions else None

    def add_submission(self, source_type: SubmissionSourceType, **submission_data):
        """添加新的提交来源记录

        Args:
            source_type: 提交来源类型
            **submission_data: 其他提交数据（如IP、用户等）

        Returns:
            GitHubRepositorySubmissionModel: 新创建的提交记录
        """
        submission = GitHubRepositorySubmissionModel(
            repository_id=self.id,
            source_type=source_type,
            **submission_data
        )
        self.submissions.append(submission)
        return submission

    @classmethod
    def create_from_url(cls, url: str, platform: str, username: str, repo_name: str,
                       source_type: SubmissionSourceType = SubmissionSourceType.URL_VALIDATION,
                       **kwargs):
        """从基本URL信息创建实例

        Args:
            url: 仓库URL
            platform: 平台名称
            username: 用户名
            repo_name: 仓库名
            source_type: 提交来源类型
            **kwargs: 其他参数（如验证状态等仓库相关参数）

        Returns:
            GitHubPendingRepositoryModel: 新创建的实例
        """
        # 分离提交来源相关参数和仓库相关参数
        submission_params = {}
        repo_params = {}

        submission_fields = {'submitted_by_ip', 'submitted_by_user', 'submitted_by_user_agent', 'submission_metadata'}

        for key, value in kwargs.items():
            if key in submission_fields:
                submission_params[key] = value
            else:
                repo_params[key] = value

        # 创建仓库实例
        repository = cls(
            repository_url=url,
            platform=platform,
            username=username,
            repository_name=repo_name,
            **repo_params
        )

        # 添加初始提交记录
        repository.add_submission(source_type, **submission_params)

        return repository
