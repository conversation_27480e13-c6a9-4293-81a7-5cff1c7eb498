#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文章生成 - 文章生成API
"""

import logging
from typing import Dict, Any, Optional
from tornado.web import HTTPError
from pydantic import BaseModel, Field
from dependency_injector.wiring import Provide, inject

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.reasoning.intelligent_article_generator.workflow.article_generation_workflow import ArticleGenerationWorkflow
from app.services.reasoning.intelligent_article_generator.models.article_models import GenerationConfig

logger = logging.getLogger(__name__)


class GenerateArticleRequest(BaseModel):
    """生成文章请求模型"""
    query: str = Field(description="文章主题查询")
    config: GenerationConfig = Field(description="生成配置")
    project_context: Optional[Dict[str, Any]] = Field(default=None, description="项目上下文")
    user_id: Optional[str] = Field(default=None, description="用户ID")


class ArticleGenerateHandler(BaseHandler):
    """文章生成处理器"""

    @inject
    def initialize(
        self,
        workflow: ArticleGenerationWorkflow = Provide[Container.article_generation_workflow]
    ):
        """初始化处理器"""
        super().initialize()
        self.workflow: ArticleGenerationWorkflow = workflow
        
    async def post(self):
        """生成文章"""
        try:
            if not self.json_body:
                raise HTTPError(400, "缺少请求数据")
                
            request = GenerateArticleRequest(**self.json_body)
            
            logger.info(f"开始生成文章，用户ID: {request.user_id}")

            # 确保工作流已初始化
            if not await self.workflow.initialize():
                raise HTTPError(500, "工作流初始化失败")

            # 调用文章生成工作流
            result = await self.workflow.generate_article(
                query=request.query,
                config=request.config,
                project_context=request.project_context
            )

            if not result:
                raise HTTPError(500, "文章生成失败")

            response_data = {
                "article": result.model_dump(),
                "generation_metadata": {
                    "user_id": request.user_id,
                    "query": request.query,
                    "target_word_count": request.config.target_word_count,
                    "sections_count": len(result.sections) if result.sections else 0
                }
            }
            
            self.success_response(response_data, "文章生成成功")
            
        except Exception as e:
            logger.error(f"生成文章失败: {str(e)}")
            raise HTTPError(500, f"生成文章失败: {str(e)}")


class ArticlePreviewHandler(BaseHandler):
    """文章预览处理器"""

    @inject
    def initialize(
        self,
        workflow: ArticleGenerationWorkflow = Provide[Container.article_generation_workflow]
    ):
        """初始化处理器"""
        super().initialize()
        self.workflow: ArticleGenerationWorkflow = workflow
        
    async def post(self):
        """预览文章（生成摘要或部分内容）"""
        try:
            if not self.json_body:
                raise HTTPError(400, "缺少请求数据")
                
            request = GenerateArticleRequest(**self.json_body)
            
            logger.info(f"开始预览文章，用户ID: {request.user_id}")

            # 这里可以实现预览逻辑，比如只生成第一段或摘要
            # 暂时返回配置信息作为预览
            preview_data = {
                "query": request.query,
                "target_word_count": request.config.target_word_count,
                "style_guide": request.config.style_guide.model_dump(),
                "project_context": request.project_context,
                "estimated_generation_time": 5  # 估算时间（分钟）
            }
            
            self.success_response(preview_data, "文章预览生成成功")
            
        except Exception as e:
            logger.error(f"预览文章失败: {str(e)}")
            raise HTTPError(500, f"预览文章失败: {str(e)}")


class ArticleValidateHandler(BaseHandler):
    """文章规划验证处理器"""
        
    async def post(self):
        """验证内容规划"""
        try:
            if not self.json_body:
                raise HTTPError(400, "缺少请求数据")
                
            query = self.json_body.get("query", "")
            config = GenerationConfig(**self.json_body.get("config", {}))

            # 执行验证逻辑
            validation_results = {
                "is_valid": True,
                "warnings": [],
                "errors": [],
                "suggestions": []
            }

            # 检查查询
            if not query or len(query.strip()) < 3:
                validation_results["errors"].append("查询主题过短，建议至少3个字符")
                validation_results["is_valid"] = False

            # 检查字数
            target_words = config.target_word_count
            if target_words > 10000:
                validation_results["warnings"].append("目标字数较多，生成时间可能较长")
            elif target_words < 500:
                validation_results["warnings"].append("目标字数较少，可能影响内容深度")

            # 检查风格配置
            if not config.style_guide:
                validation_results["warnings"].append("未设置风格指南，将使用默认配置")
                
            self.success_response(validation_results, "规划验证完成")
            
        except Exception as e:
            logger.error(f"验证规划失败: {str(e)}")
            raise HTTPError(500, f"验证规划失败: {str(e)}")


class ArticleStatusHandler(BaseHandler):
    """文章生成状态处理器"""
        
    async def get(self):
        """获取生成状态（如果有异步生成任务）"""
        try:
            task_id = self.get_argument("task_id", None)
            if not task_id:
                raise HTTPError(400, "缺少任务ID")
                
            # 这里可以实现任务状态查询逻辑
            # 暂时返回模拟数据
            status_data = {
                "task_id": task_id,
                "status": "completed",  # pending, running, completed, failed
                "progress": 100,
                "message": "文章生成已完成",
                "result": None  # 如果完成，这里包含结果
            }
            
            self.success_response(status_data, "状态查询成功")
            
        except HTTPError:
            raise
        except Exception as e:
            logger.error(f"获取生成状态失败: {str(e)}")
            raise HTTPError(500, f"获取生成状态失败: {str(e)}")


class ArticleTemplatesHandler(BaseHandler):
    """文章模板处理器"""
        
    async def get(self):
        """获取可用的模板"""
        try:
            # 这里可以实现模板查询逻辑
            # 暂时返回模拟数据
            templates = [
                {
                    "id": "technical_article",
                    "name": "技术文章",
                    "description": "适用于技术教程、API文档等",
                    "sections": ["介绍", "背景", "实现方案", "代码示例", "总结"]
                },
                {
                    "id": "business_article",
                    "name": "商业文章",
                    "description": "适用于产品介绍、市场分析等",
                    "sections": ["概述", "市场背景", "产品特性", "竞争优势", "结论"]
                },
                {
                    "id": "tutorial_article",
                    "name": "教程文章",
                    "description": "适用于步骤指导、学习教程等",
                    "sections": ["准备工作", "步骤详解", "常见问题", "进阶技巧", "总结"]
                }
            ]
            
            self.success_response({"templates": templates}, "模板获取成功")
            
        except Exception as e:
            logger.error(f"获取模板失败: {str(e)}")
            raise HTTPError(500, f"获取模板失败: {str(e)}")
