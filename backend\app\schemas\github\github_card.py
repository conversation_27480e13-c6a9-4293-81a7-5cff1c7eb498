"""
GitHub项目卡片相关Schema
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field

class GitHubCardBase(BaseModel):
    """GitHub卡片基础模型"""
    content: str = Field(..., description="卡片内容")
    title: str = Field(..., description="标题")
    sort_order: int = Field(default=0, description="卡片排序顺序")

class GitHubCardCreate(GitHubCardBase):
    """GitHub卡片创建模型"""
    project_id: str = Field(..., description="所属项目ID")

class GitHubCardUpdate(BaseModel):
    """GitHub卡片更新模型"""
    content: Optional[str] = Field(None, description="卡片内容")
    order: Optional[int] = Field(None, description="卡片排序顺序")

class GitHubCard(GitHubCardBase):
    """GitHub卡片模型"""
    id: str = Field(..., description="卡片ID")
    project_id: str = Field(..., description="所属项目ID")
    likes: int = Field(default=0, description="点赞数")
    favorites: int = Field(default=0, description="收藏数")
    dislikes: int = Field(default=0, description="不喜欢数")
