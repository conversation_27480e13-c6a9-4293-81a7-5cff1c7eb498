"""
Git版本控制分析相关的数据模型

定义与Git仓库分析相关的数据结构
"""
from datetime import datetime
from typing import Dict, List, Optional, Any, Union

from pydantic import BaseModel, Field


class CommitFrequency(BaseModel):
    """提交频率
    
    表示某个时间段的提交次数
    """
    date: str = Field(..., description="日期")
    count: int = Field(0, description="提交次数")


class TimeDistribution(BaseModel):
    """时间分布
    
    表示提交在一天中的小时和星期几的分布情况
    """
    hourly_distribution: List[int] = Field(default_factory=list, description="24小时分布 (0-23)")
    weekday_distribution: List[int] = Field(default_factory=list, description="星期几分布 (0-6, 0是周一)")


class GitActivityAnalysis(BaseModel):
    """Git活跃度分析
    
    提供仓库提交活跃度的综合分析
    """
    total_commits: int = Field(0, description="总提交数")
    time_period_days: int = Field(0, description="分析时间范围(天)")
    avg_commits_per_day: float = Field(0.0, description="平均每日提交数")
    max_commits_in_day: int = Field(0, description="单日最大提交数")
    total_additions: int = Field(0, description="总增加行数")
    total_deletions: int = Field(0, description="总删除行数")
    daily_commit_frequency: List[CommitFrequency] = Field(default_factory=list, description="每日提交频率")
    weekly_commit_frequency: List[CommitFrequency] = Field(default_factory=list, description="每周提交频率")
    monthly_commit_frequency: List[CommitFrequency] = Field(default_factory=list, description="每月提交频率")
    time_distribution: TimeDistribution = Field(default_factory=TimeDistribution, description="提交时间分布")


class ChangeTrend(BaseModel):
    """变更趋势
    
    表示某个时间段的代码变更趋势
    """
    time_period: str = Field(..., description="时间段")
    additions: int = Field(0, description="增加行数")
    deletions: int = Field(0, description="删除行数")
    net_change: int = Field(0, description="净变更量")
    commits: int = Field(0, description="提交次数")


class AuthorContribution(BaseModel):
    """作者贡献
    
    表示某个贡献者的贡献情况
    """
    name: str = Field(..., description="贡献者姓名")
    email: str = Field(..., description="贡献者邮箱")
    commit_count: int = Field(0, description="提交次数")
    additions: int = Field(0, description="增加行数")
    deletions: int = Field(0, description="删除行数")
    first_commit: Optional[datetime] = Field(None, description="首次提交时间")
    latest_commit: Optional[datetime] = Field(None, description="最近提交时间")
    active_days: int = Field(0, description="活跃天数")


class FileHotspot(BaseModel):
    """文件热点
    
    表示经常被修改的文件
    """
    file_path: str = Field(..., description="文件路径")
    change_frequency: int = Field(0, description="修改频率")
    latest_change: Optional[datetime] = Field(None, description="最近修改时间")
    file_type: str = Field("", description="文件类型")


class CommitPatternAnalysis(BaseModel):
    """提交模式分析
    
    分析开发者的提交行为模式
    """
    avg_commit_message_length: float = Field(0.0, description="平均提交信息长度")
    emoji_usage_rate: float = Field(0.0, description="Emoji使用率")
    issue_reference_rate: float = Field(0.0, description="Issue引用率")
    common_prefixes: Dict[str, float] = Field(default_factory=dict, description="常用提交类型前缀")
    work_hour_commit_rate: float = Field(0.0, description="工作时间提交率")
    weekend_commit_rate: float = Field(0.0, description="周末提交率")


class GitRepositoryAnalysis(BaseModel):
    """Git仓库分析结果
    
    提供Git仓库分析的综合结果
    """
    repository_name: str = Field(..., description="仓库名称")
    repository_path: str = Field(..., description="仓库路径")
    analysis_time: datetime = Field(..., description="分析时间")
    branch_analyzed: str = Field(..., description="分析的分支")
    commit_count: int = Field(0, description="分析的提交数量")
    time_period_days: int = Field(0, description="分析时间范围(天)")
    activity_analysis: GitActivityAnalysis = Field(default_factory=GitActivityAnalysis, description="活跃度分析")
    change_trends: List[ChangeTrend] = Field(default_factory=list, description="变更趋势")
    contributor_analysis: List[AuthorContribution] = Field(default_factory=list, description="贡献者分析")
    file_hotspots: List[FileHotspot] = Field(default_factory=list, description="文件热点")
    commit_patterns: CommitPatternAnalysis = Field(default_factory=CommitPatternAnalysis, description="提交模式分析")


class GitAnalysisRequest(BaseModel):
    """Git分析请求
    
    请求进行Git仓库分析的参数
    """
    repository_path: str = Field(..., description="仓库路径")
    branch: Optional[str] = Field(None, description="分支名称，默认为当前分支")
    days: int = Field(365, description="分析的时间范围（天数），默认为365天")
    max_commits: int = Field(1000, description="最大分析提交数量，默认为1000")
