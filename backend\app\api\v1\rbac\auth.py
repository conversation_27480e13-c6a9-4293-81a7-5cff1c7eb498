"""
认证相关的API处理器
"""
from http import HTT<PERSON>tatus
from typing import Optional

import structlog
import secrets
import base64

from dependency_injector.wiring import inject, Provide
from tornado.web import HTTPError
from datetime import datetime, timedelta
from app.api.base import BaseHandler
from app.core.config import settings
from app.core.di.containers import Container
from app.services.rbac.auth import AuthService
from app.schemas.rbac.user import UserLoginRequest, UserLogoutRequest
from app.schemas.rbac.token import RefreshTokenRequest
from app.core.middleware import auth_middleware
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.rbac.phone import PhoneService
from app.services.rbac.user import UserService
from app.services.rbac.email import EmailService
from app.schemas.rbac.password_reset import (
    PasswordResetRequest,
    PasswordResetTokenVerify,
    PasswordResetComplete
)
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)


class BindChangeAuthVerificationCodeHandler(BaseHandler):
    """更换绑定验证码处理器"""

    @inject
    def initialize(
            self,
            email_service: EmailService = Provide[Container.email_service],
            phone_service: PhoneService = Provide[Container.phone_service],
            user_service: UserService = Provide[Container.user_service],
    ):
        super().initialize()
        self.email_service = email_service
        self.phone_service = phone_service
        self.user_service = user_service

    async def post(self) -> None:
        """发送验证码"""
        try:
            email = self.get_argument("email", None)
            phone = self.get_argument("phone", None)

            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            success = True
            error_messages = []

            # 处理邮箱验证码
            if email:
                # 检查邮箱是否已注册
                email_exists = await self.user_service.check_email_exists(email)
                if email_exists:
                    error_messages.append("该邮箱已注册过无法发送验证码")
                else:
                    # 发送邮箱验证码
                    if not self.email_service.send_bind_change_verification_code(email):
                        error_messages.append("邮箱验证码发送失败")
                        success = False
                    else:
                        logger.info("邮箱验证码发送成功", email=email)

            # 处理手机验证码
            if phone:
                # 检查手机号是否已注册
                phone_exists = await self.user_service.check_phone_exists(phone)
                if phone_exists:
                    error_messages.append("该手机号已注册过无法发送验证码")
                    success = False
                else:
                    if not self.phone_service.send_bind_change_verification_code(phone):
                        error_messages.append("手机验证码发送失败")
                        success = False

            if not success:
                self.write_error(500, error_message="; ".join(error_messages))
                return

            self.success_response()

        except Exception as e:
            logger.error("发送验证码失败", error=str(e))
            self.write_error(500, error_message="发送验证码失败")


class BindChangeAuthVerificationCodeOriginalHandler(BaseHandler):
    """更换绑定验证码处理器"""

    @inject
    def initialize(
            self,
            email_service: EmailService = Provide[Container.email_service],
            phone_service: PhoneService = Provide[Container.phone_service],
            user_service: UserService = Provide[Container.user_service],
    ):
        super().initialize()
        self.email_service = email_service
        self.phone_service = phone_service
        self.user_service = user_service

    async def post(self) -> None:
        """发送验证码"""
        try:
            solve_email = self.get_argument("solve_email", None)
            solve_phone = self.get_argument("solve_phone", None)
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)

            if not solve_email and not solve_phone:
                self.write_error(400, error_message="未知验证")
                return

            success = True
            error_messages = []

            # 处理邮箱验证码
            if solve_email:
                # 检查邮箱是否已注册
                email_exists = await self.user_service.check_user_email_exists(user_id)
                if not email_exists:
                    error_messages.append("该用户未绑定邮箱")
                else:
                    # 发送邮箱验证码
                    if not self.email_service.send_bind_change_verification_code(email_exists):
                        error_messages.append("邮箱验证码发送失败")
                        success = False
                    else:
                        logger.info("邮箱验证码发送成功", email=email_exists)

            # 处理手机验证码
            if solve_phone:
                # 检查手机号是否已注册
                phone_exists = await self.user_service.check_user_phone_exists(user_id)
                if not phone_exists:
                    error_messages.append("该用户未绑定手机")
                    success = False
                else:
                    if not self.phone_service.send_bind_change_verification_code(phone_exists):
                        error_messages.append("手机验证码发送失败")
                        success = False

            if not success:
                self.write_error(500, error_message="; ".join(error_messages))
                return

            self.success_response()

        except Exception as e:
            logger.error("发送验证码失败", error=str(e))
            self.write_error(500, error_message="发送验证码失败")


class BindChangeAuthHandler(BaseHandler):
    """更换绑定处理器"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service],
            email_service: EmailService = Provide[Container.email_service],
            auth_service: AuthService = Provide[Container.auth_service],
            phone_service: PhoneService = Provide[Container.phone_service],
    ):
        super().initialize()
        self.user_service = user_service
        self.email_service = email_service
        self.auth_service = auth_service
        self.phone_service = phone_service

    async def post(self):
        """处理更换绑定请求"""
        try:
            # 验证请求数据
            data = self.json_body
            email = data.get('email')
            phone = data.get('phone')
            verification_code = data.get('validate_code')
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            data["user_id"] = user_id

            # 验证请求数据
            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            if email and phone:
                self.write_error(400, error_message="不能同时绑定邮箱和手机号")
                return

            # 记录验证开始
            logger.info(
                "开始验证码验证",
                extra={
                    "email": email,
                    "phone": phone,
                    "verification_code": verification_code,
                }
            )

            # 验证验证码
            verification_success = False
            if email:
                verification_success = self.email_service.verify_bind_change_codes(email, verification_code)
            elif phone:
                verification_success = self.phone_service.verify_bind_change_phone_code(phone, verification_code)

            if not verification_success:
                self.write_error(500, error_message="验证码错误或已过期")
                return

            # 更新用户信息
            from app.schemas.rbac.user import UserUpdateRequest
            data = UserUpdateRequest.model_validate(data)
            is_bild = False
            user = await self.user_service.update(data, is_bild)

            # 创建新的token
            access_token = self.auth_service.create_access_token(
                data={"sub": user.id, "is_superuser": user.is_superuser}
            )
            refresh_token = self.auth_service.create_refresh_token(
                data={"sub": user.id}
            )

            # 创建令牌
            token = await self.user_service.create_token(
                user_id=user.id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=datetime.now() + timedelta(
                    minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
                )
            )

            response_data = {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "phone": user.phone,
                }
            }

            self.success_response(data=response_data)

        except ValueError as e:
            logger.warning("更换绑定请求数据无效", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("更换绑定时发生错误", error=str(e))
            self.write_error(500, error_message="更换绑定时发生错误")



class BindChangeOriginalAuthHandler(BaseHandler):
    """检测原手机邮箱验证"""

    @inject
    def initialize(
            self,
            user_service: UserService = Provide[Container.user_service],
            email_service: EmailService = Provide[Container.email_service],
            auth_service: AuthService = Provide[Container.auth_service],
            phone_service: PhoneService = Provide[Container.phone_service],
    ):
        super().initialize()
        self.user_service = user_service
        self.email_service = email_service
        self.auth_service = auth_service
        self.phone_service = phone_service

    async def post(self):
        """处理更换绑定请求"""
        try:
            # 验证请求数据
            data = self.json_body
            email = data.get('email')
            phone = data.get('phone')
            verification_code = data.get('validate_code')
            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            data["user_id"] = user_id

            # 验证请求数据
            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            if email and phone:
                self.write_error(400, error_message="不能同时验证原来的邮箱和手机号")
                return

            # 记录验证开始
            logger.info(
                "开始验证码验证",
                extra={
                    "email": email,
                    "phone": phone,
                    "verification_code": verification_code,
                }
            )

            # 验证验证码
            verification_success = False
            if email:
                verification_success = self.email_service.verify_bind_change_codes(email, verification_code)
            elif phone:
                verification_success = self.phone_service.verify_bind_change_phone_code(phone, verification_code)

            if not verification_success:
                self.write_error(500, error_message="验证码错误或已过期")
                return

            # 更新用户信息
            from app.schemas.rbac.user import UserUpdateRequest
            data = UserUpdateRequest.model_validate(data)
            is_bild = False
            user = await self.user_service.update(data, is_bild)

            # 创建新的token
            access_token = self.auth_service.create_access_token(
                data={"sub": user.id, "is_superuser": user.is_superuser}
            )
            refresh_token = self.auth_service.create_refresh_token(
                data={"sub": user.id}
            )

            # 创建令牌
            token = await self.user_service.create_token(
                user_id=user.id,
                access_token=access_token,
                refresh_token=refresh_token,
                expires_at=datetime.now() + timedelta(
                    minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES
                )
            )

            response_data = {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "phone": user.phone,
                }
            }

            self.success_response(data=response_data)

        except ValueError as e:
            logger.warning("更换绑定请求数据无效", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("更换绑定时发生错误", error=str(e))
            self.write_error(500, error_message="更换绑定时发生错误")

class FastAuthVerificationCodeHandler(BaseHandler):
    """快捷登录-验证码处理器"""

    @inject
    def initialize(
            self,
            auth_service: AuthService = Provide[Container.auth_service],
            user_service: UserService = Provide[Container.user_service],
            phone_service: PhoneService = Provide[Container.phone_service],
            email_service: EmailService = Provide[Container.email_service]
    ):
        """初始化处理器

        Args:
            auth_service: 认证服务
            user_service: 用户服务
            phone_service: 手机验证码服务
        """
        super().initialize()
        self.auth_service = auth_service
        self.user_service = user_service
        self.phone_service = phone_service
        self.email_service = email_service

    async def post(self):
        """处理快捷登录请求"""
        try:
            # 获取请求数据
            email = self.get_argument("email", None)
            phone = self.get_argument("phone", None)

            if not email and not phone:
                self.write_error(400, error_message="请提供邮箱或手机号")
                return

            success = True
            error_messages = []

            # 处理邮箱验证码
            if email:
                if not self.email_service.send_fast_login_verification_code(email):
                    error_messages.append("邮箱验证码发送失败")
                    success = False
                else:
                    logger.info("邮箱验证码发送成功", email=email)

            # 处理手机验证码
            if phone:
                if not self.phone_service.send_fast_auth_phone_verification_code(phone):
                    error_messages.append("手机验证码发送失败")
                    success = False
                else:
                    logger.info("手机验证码发送成功", email=email)

            if not success:
                self.write_error(500, error_message="; ".join(error_messages))
                return

            self.success_response()

        except ValueError as e:
            logger.warning("快捷登录请求数据无效", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("处理快捷登录请求时发生错误", error=str(e))
            self.write_error(500, error_message="处理快捷登录请求时发生错误")


class FastAuthHandler(BaseHandler):
    """快捷登录处理器"""

    @inject
    def initialize(
            self,
            auth_service: AuthService = Provide[Container.auth_service],
            user_service: UserService = Provide[Container.user_service],
            phone_service: PhoneService = Provide[Container.phone_service],
            email_service: EmailService = Provide[Container.email_service]
    ):
        """初始化处理器

        Args:
            auth_service: 认证服务
            user_service: 用户服务
            phone_service: 手机验证码服务
        """
        super().initialize()
        self.auth_service = auth_service
        self.user_service = user_service
        self.phone_service = phone_service
        self.email_service = email_service

    async def post(self):
        """处理快捷登录请求"""
        try:
            # 获取请求数据
            data = self.json_body
            phone = data.get('phone')
            email = data.get('email')
            verification_code = data.get('verification_code')

            if (not phone) and (not email):
                self.write_error(400, error_message="手机号和邮箱不能同时为空")
                return

            if (phone) and (email):
                self.write_error(400, error_message="手机号和邮箱不能同时验证")
                return

            if not verification_code:
                self.write_error(400, error_message="验证码为空")
                return

            # 判断验证的手机/邮箱 的验证码是否输入正确，判断该手机/邮箱是否注册过:
            user = None
            token = None
            if phone:
                # 检查手机号是否已注册
                if not self.phone_service.verify_fast_auth_phone_code(phone , verification_code):
                    raise ValueError("手机号或验证码输入错误")
                user = await self.user_service.get_by_phone(phone)

            if email:
                # 检查邮箱是否已注册
                if not self.email_service.verify_fast_login_codes(email, verification_code):
                    raise ValueError("邮箱账号或验证码输入错误")
                user = await self.user_service.get_by_email(email)


            if not user:
                # 如果没有注册过 就快捷注册
                user = await self.user_service.fast_register(phone, email)


            # 快捷登陆
            access_token = self.auth_service.create_access_token(
                data={"sub": user.id, "is_superuser": user.is_superuser}
            )
            refresh_token = self.auth_service.create_refresh_token(
                data={"sub": user.id}
            )
            # 创建访问令牌
            token = await self.user_service.create_token(user_id=user.id, access_token=access_token,
                                                         refresh_token=refresh_token,
                                                         expires_at=datetime.now() + timedelta(
                                                             minutes=settings.security.JWT_ACCESS_TOKEN_EXPIRE_MINUTES))

            response_data = {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                    "phone": user.phone,
                }
            }

            self.success_response(data=response_data)

        except ValueError as e:
            logger.warning("快捷登录请求数据无效", error=str(e))
            self.write_error(400, error_message=str(e))
        except Exception as e:
            logger.error("处理快捷登录请求时发生错误", error=str(e))
            self.write_error(500, error_message="处理快捷登录请求时发生错误")


class LoginHandler(BaseHandler):
    """用户登录处理器"""

    @inject
    def initialize(
            self,
            auth_service: AuthService = Provide[Container.auth_service]
    ):
        """初始化处理器

        Args:
            auth_service: 认证服务
        """
        super().initialize()
        self.auth_service = auth_service

    def set_default_headers(self):
        """设置默认的 CORS 头部"""
        super().set_default_headers()
        self.set_header("Access-Control-Allow-Origin", "*")  # 或者指定允许的域名
        self.set_header("Access-Control-Allow-Headers", "Content-Type, Authorization")
        self.set_header("Access-Control-Allow-Methods", "POST, OPTIONS")

    async def options(self):
        """处理 OPTIONS 预检请求"""
        self.set_status(204)  # No Content
        self.finish()

    async def post(self):
        """处理登录请求"""
        try:
            # 验证请求数据
            data = UserLoginRequest.model_validate(self.json_body)

            # 验证用户凭据
            user, token = await self.auth_service.login(
                data
            )

            # 返回用户信息和令牌
            data = {
                "user": user.model_dump(),
                "token": token.model_dump()
            }
            self.success_response(data=data)


        except ValueError as e:
            error_msg = "用户密码错误"
            logger.warning(error_msg, error=str(e))
            self.write_error(500, error_message=str(e))

        except Exception as e:
            error_msg = "处理登录请求时发生错误"
            logger.error(error_msg, error=str(e))
            self.write_error(500, error_message=error_msg)


@auth_middleware(required=True)
class RefreshTokenHandler(BaseHandler):
    """刷新令牌处理器"""

    @inject
    def initialize(
            self,
            auth_service: AuthService = Provide[Container.auth_service]
    ):
        """初始化处理器

        Args:
            auth_service: 认证服务
        """
        super().initialize()
        self.auth_service = auth_service

    async def post(self):
        """处理刷新令牌请求"""
        try:
            # 验证请求数据
            data = RefreshTokenRequest.model_validate(self.json_body)

            # 验证刷新令牌
            token = await self.auth_service.refresh_token(data)
            if not token:
                raise HTTPError(401, "无效的刷新令牌")

            # 返回新的访问令牌
            data = {
                "token": token.model_dump()
            }
            self.success_response(data=data)
        except ValueError as e:
            error_msg = "刷新令牌请求数据无效"
            logger.warning(error_msg, error=str(e))
            self.write_error(500, error_message=error_msg)
        except Exception as e:
            error_msg = "处理刷新令牌请求时发生错误"
            logger.error("处理刷新令牌请求时发生错误", error=str(e))
            self.write_error(500, error_message=error_msg)


@auth_middleware(required=True)
class LogoutHandler(BaseHandler):
    """退出登录处理器"""

    @inject
    def initialize(
            self,
            auth_service: AuthService = Provide[Container.auth_service]
    ):
        """初始化处理器

        Args:
            auth_service: 认证服务
        """
        super().initialize()
        self.auth_service = auth_service

    async def post(self):
        """处理退出登录请求"""
        try:
            try:
                if self.json_body:
                    data = UserLogoutRequest.model_validate(self.json_body)
            except Exception as e:
                logger.info("未知的退出登录请求数据", error=str(e))
                pass
            # 获取当前用户的访问令牌
            access_token = self.request.headers.get("Authorization", "").replace("Bearer ", "")
            user = await self.auth_service.verify_access_token(access_token)
            if not user:
                raise HTTPError(401, "无效的访问令牌")
            data = UserLogoutRequest.model_validate({"token": access_token, "user_id": user.id})
            # 注销令牌
            await self.auth_service.logout(data)
            self.success_response()

        except Exception as e:
            logger.error("处理退出登录请求时发生错误", error=str(e))
            # self.write_error(500,{"message": "处理退出登录请求时发生错误"})

