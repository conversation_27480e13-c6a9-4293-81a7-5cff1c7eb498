"""
OAuth认证相关的API处理器
"""
from typing import Optional, Dict, Any

import structlog
from dependency_injector.wiring import inject, Provide
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.rbac.oauth import OAuthService
from app.schemas.rbac.oauth import OAuthAuthorizeRequest, OAuthCallbackRequest
from app.core.middleware import auth_middleware, require_auth

logger = structlog.get_logger(__name__)

class OAuthProvidersHandler(BaseHandler):
    """OAuth提供商列表处理器"""
    
    @inject
    def initialize(
        self,
        oauth_service: OAuthService = Provide[Container.oauth_service]
    ):
        """初始化处理器
        
        Args:
            oauth_service: OAuth服务
        """
        super().initialize()
        self.oauth_service = oauth_service
    
    async def get(self):
        """获取OAuth提供商列表"""
        try:
            providers = await self.oauth_service.get_providers()
            self.write(providers.model_dump())
        except Exception as e:
            logger.error("获取OAuth提供商列表失败", error=str(e))
            raise HTTPError(500, f"获取OAuth提供商列表失败: {str(e)}")

class OAuthAuthorizeHandler(BaseHandler):
    """OAuth授权处理器"""
    
    @inject
    def initialize(
        self,
        oauth_service: OAuthService = Provide[Container.oauth_service]
    ):
        """初始化处理器
        
        Args:
            oauth_service: OAuth服务
        """
        super().initialize()
        self.oauth_service = oauth_service

    async def post(self):
        """获取OAuth授权URL"""
        try:

            provider = self.get_argument("providers", None)
            # 验证请求数据
            # data = OAuthAuthorizeRequest.model_validate(self.json_body)
            # 获取授权URL
            authorize_url = await self.oauth_service.get_authorize_url(
                provider=provider
            )
            
            # 返回授权URL
            self.write({
                "authorize_url": authorize_url
            })
        except ValueError as e:
            logger.error("获取OAuth授权URL失败", error=str(e))
            self.write_error(500, error_message="获取OAuth授权URL失败" +str(e))
        except Exception as e:
            logger.error("获取OAuth授权URL失败", error=str(e))
            self.write_error(500, error_message=f"获取OAuth授权URL失败: {str(e)}")


class OAuthGithubCallbackHandler(BaseHandler):
    """OAuth回调处理器"""
    
    @inject
    def initialize(
        self,
        oauth_service: OAuthService = Provide[Container.oauth_service]
    ):
        """初始化处理器
        
        Args:
            oauth_service: OAuth服务
        """
        super().initialize()
        self.oauth_service = oauth_service
    
    async def get(self):
        """处理OAuth回调"""
        try:

            code = self.get_argument("code", None)
            state = self.get_argument("state", None)
            user, token = await self.oauth_service.handle_github_callback(
                code=code
            )

            if user.needs_email_binding:
                response_data = {
                    "user_id": str(user.id),
                    "needs_email_binding": True
                }
                return self.success_response(data=response_data)

            response_data = {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                }
            }

            return self.success_response(data=response_data)

        except ValueError as e:
            logger.error("处理GithubOAuth回调失败", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("处理GithubOAuth回调失败", error=str(e))
            self.write_error(500, error_message=f"处理GithubOAuth回调失败: {str(e)}")


class OAuthGiteeCallbackHandler(BaseHandler):
    """OAuth回调处理器"""

    @inject
    def initialize(
            self,
            oauth_service: OAuthService = Provide[Container.oauth_service]
    ):
        """初始化处理器

        Args:
            oauth_service: OAuth服务
        """
        super().initialize()
        self.oauth_service = oauth_service

    async def get(self):
        """处理OAuth回调"""
        try:

            code = self.get_argument("code", None)
            state = self.get_argument("state", None)
            user, token = await self.oauth_service.handle_gitee_callback(
                code=code
            )

            if user.needs_email_binding:
                response_data = {
                    "user_id": str(user.id),
                    "needs_email_binding": True
                }
                return self.success_response(data=response_data)

            response_data = {
                "access_token": token.access_token,
                "refresh_token": token.refresh_token,
                "token_type": "bearer",
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email,
                }
            }

            return self.success_response(data=response_data)

        except ValueError as e:
            logger.error("处理GiteeOAuth回调失败", error=str(e))
            self.write_error(500, error_message=str(e))
        except Exception as e:
            logger.error("处理GiteeOAuth回调失败", error=str(e))
            self.write_error(500, error_message=f"处理GiteeOAuth回调失败: {str(e)}")



class OAuthAccountsHandler(BaseHandler):
    """OAuth账号处理器"""
    
    @inject
    def initialize(
        self,
        oauth_service: OAuthService = Provide[Container.oauth_service]
    ):
        """初始化处理器
        
        Args:
            oauth_service: OAuth服务
        """
        super().initialize()
        self.oauth_service = oauth_service
    
    @require_auth(required=True)
    async def get(self):
        """获取用户的OAuth账号列表"""
        try:
            # 从认证中获取用户ID
            user_id = self.current_user.id
            if not user_id:
                self.send_error(401, message="未授权")
                return
                
            accounts = await self.oauth_service.get_oauth_accounts(user_id)
            self.write(accounts.model_dump())
        except Exception as e:
            logger.error("获取OAuth账号列表失败", error=str(e))
            raise HTTPError(500, f"获取OAuth账号列表失败: {str(e)}")

class OAuthAccountDeleteHandler(BaseHandler):
    """OAuth账号删除处理器"""
    
    @inject
    def initialize(
        self,
        oauth_service: OAuthService = Provide[Container.oauth_service]
    ):
        """初始化处理器
        
        Args:
            oauth_service: OAuth服务
        """
        super().initialize()
        self.oauth_service = oauth_service
    
    @require_auth(required=True)
    async def delete(self, account_id: str):
        """删除OAuth账号"""
        try:
            # 从认证中获取用户ID
            user_id = self.current_user.id
            if not user_id:
                self.send_error(401, message="未授权")
                return
                
            success = await self.oauth_service.delete_oauth_account(account_id, user_id)
            if success:
                self.set_status(204)
                self.finish()
            else:
                self.send_error(404, message="账号不存在或无权删除")
        except Exception as e:
            logger.error("删除OAuth账号失败", error=str(e), account_id=account_id)
            raise HTTPError(500, f"删除OAuth账号失败: {str(e)}")
