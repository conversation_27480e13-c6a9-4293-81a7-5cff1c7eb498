"""
MongoDB提供者模块
"""
from typing import Any, Optional, Dict, List, Union
import structlog
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection
from pymongo import MongoClient
from pymongo.database import Database
from pymongo.collection import Collection
from dependency_injector import providers

logger = structlog.get_logger(__name__)

class AsyncMongoProvider(providers.Provider):
    """MongoDB异步客户端提供者"""
    
    def __init__(self, mongo_client: AsyncIOMotorClient, database_name: str):
        """初始化MongoDB客户端提供者
        
        Args:
            mongo_client: MongoDB异步客户端
            database_name: 数据库名称
        """
        self._client = mongo_client
        self._database_name = database_name
        self._database = None
        super().__init__()
    
    def __call__(self) -> AsyncIOMotorClient:
        """获取MongoDB客户端实例
        
        Returns:
            AsyncIOMotorClient: MongoDB异步客户端实例
        """
        return self._client
    
    @property
    def database(self) -> AsyncIOMotorDatabase:
        """获取数据库实例
        
        Returns:
            AsyncIOMotorDatabase: 数据库实例
        """
        if self._database is None:
            self._database = self._client[self._database_name]
        return self._database
    
    def get_collection(self, collection_name: str) -> AsyncIOMotorCollection:
        """获取集合实例
        
        Args:
            collection_name: 集合名称
            
        Returns:
            AsyncIOMotorCollection: 集合实例
        """
        return self.database[collection_name]
    
    async def insert_one(self, collection_name: str, document: Dict[str, Any]) -> Optional[str]:
        """插入单个文档
        
        Args:
            collection_name: 集合名称
            document: 要插入的文档
            
        Returns:
            Optional[str]: 插入文档的ID，失败时返回None
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.insert_one(document)
            logger.info("MongoDB文档插入成功", 
                       collection=collection_name, 
                       document_id=str(result.inserted_id))
            return str(result.inserted_id)
        except Exception as e:
            logger.error("MongoDB文档插入失败", 
                        collection=collection_name, 
                        error=str(e))
            return None
    
    async def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> List[str]:
        """插入多个文档
        
        Args:
            collection_name: 集合名称
            documents: 要插入的文档列表
            
        Returns:
            List[str]: 插入文档的ID列表
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.insert_many(documents)
            inserted_ids = [str(id_) for id_ in result.inserted_ids]
            logger.info("MongoDB批量文档插入成功", 
                       collection=collection_name, 
                       count=len(inserted_ids))
            return inserted_ids
        except Exception as e:
            logger.error("MongoDB批量文档插入失败", 
                        collection=collection_name, 
                        error=str(e))
            return []
    
    async def find_one(self, collection_name: str, filter_dict: Dict[str, Any] = None, 
                      projection: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """查找单个文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            projection: 投影字段
            
        Returns:
            Optional[Dict[str, Any]]: 查找到的文档，未找到时返回None
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.find_one(filter_dict or {}, projection)
            if result:
                # 转换ObjectId为字符串
                if '_id' in result:
                    result['_id'] = str(result['_id'])
            return result
        except Exception as e:
            logger.error("MongoDB文档查找失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return None
    
    async def find_many(self, collection_name: str, filter_dict: Dict[str, Any] = None,
                       projection: Dict[str, Any] = None, limit: int = None, 
                       skip: int = None, sort: List[tuple] = None) -> List[Dict[str, Any]]:
        """查找多个文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            projection: 投影字段
            limit: 限制返回数量
            skip: 跳过数量
            sort: 排序条件
            
        Returns:
            List[Dict[str, Any]]: 查找到的文档列表
        """
        try:
            collection = self.get_collection(collection_name)
            cursor = collection.find(filter_dict or {}, projection)
            
            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)
                
            documents = await cursor.to_list(length=limit)
            
            # 转换ObjectId为字符串
            for doc in documents:
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                    
            return documents
        except Exception as e:
            logger.error("MongoDB文档批量查找失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return []
    
    async def update_one(self, collection_name: str, filter_dict: Dict[str, Any], 
                        update_dict: Dict[str, Any], upsert: bool = False) -> bool:
        """更新单个文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容
            upsert: 是否在文档不存在时插入
            
        Returns:
            bool: 是否更新成功
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.update_one(filter_dict, update_dict, upsert=upsert)
            success = result.modified_count > 0 or (upsert and result.upserted_id is not None)
            if success:
                logger.info("MongoDB文档更新成功", 
                           collection=collection_name,
                           modified_count=result.modified_count)
            return success
        except Exception as e:
            logger.error("MongoDB文档更新失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return False
    
    async def update_many(self, collection_name: str, filter_dict: Dict[str, Any], 
                         update_dict: Dict[str, Any]) -> int:
        """更新多个文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容
            
        Returns:
            int: 更新的文档数量
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.update_many(filter_dict, update_dict)
            logger.info("MongoDB批量文档更新成功", 
                       collection=collection_name,
                       modified_count=result.modified_count)
            return result.modified_count
        except Exception as e:
            logger.error("MongoDB批量文档更新失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return 0
    
    async def delete_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> bool:
        """删除单个文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            
        Returns:
            bool: 是否删除成功
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.delete_one(filter_dict)
            success = result.deleted_count > 0
            if success:
                logger.info("MongoDB文档删除成功", 
                           collection=collection_name,
                           deleted_count=result.deleted_count)
            return success
        except Exception as e:
            logger.error("MongoDB文档删除失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return False
    
    async def delete_many(self, collection_name: str, filter_dict: Dict[str, Any]) -> int:
        """删除多个文档
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            
        Returns:
            int: 删除的文档数量
        """
        try:
            collection = self.get_collection(collection_name)
            result = await collection.delete_many(filter_dict)
            logger.info("MongoDB批量文档删除成功", 
                       collection=collection_name,
                       deleted_count=result.deleted_count)
            return result.deleted_count
        except Exception as e:
            logger.error("MongoDB批量文档删除失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return 0
    
    async def count_documents(self, collection_name: str, filter_dict: Dict[str, Any] = None) -> int:
        """统计文档数量
        
        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            
        Returns:
            int: 文档数量
        """
        try:
            collection = self.get_collection(collection_name)
            count = await collection.count_documents(filter_dict or {})
            return count
        except Exception as e:
            logger.error("MongoDB文档计数失败", 
                        collection=collection_name, 
                        filter=filter_dict,
                        error=str(e))
            return 0
    
    async def create_index(self, collection_name: str, keys: Union[str, List[tuple]], 
                          unique: bool = False, background: bool = True) -> Optional[str]:
        """创建索引
        
        Args:
            collection_name: 集合名称
            keys: 索引键
            unique: 是否唯一索引
            background: 是否后台创建
            
        Returns:
            Optional[str]: 索引名称，失败时返回None
        """
        try:
            collection = self.get_collection(collection_name)
            index_name = await collection.create_index(keys, unique=unique, background=background)
            logger.info("MongoDB索引创建成功", 
                       collection=collection_name,
                       index_name=index_name)
            return index_name
        except Exception as e:
            logger.error("MongoDB索引创建失败", 
                        collection=collection_name, 
                        keys=keys,
                        error=str(e))
            return None
    
    def reset(self) -> None:
        """重置提供者状态"""
        self._database = None


class SyncMongoProvider(providers.Provider):
    """MongoDB同步客户端提供者"""

    def __init__(self, mongo_client: MongoClient, database_name: str):
        """初始化MongoDB同步客户端提供者

        Args:
            mongo_client: MongoDB同步客户端
            database_name: 数据库名称
        """
        self._client = mongo_client
        self._database_name = database_name
        self._database = None
        super().__init__()

    def __call__(self) -> MongoClient:
        """获取MongoDB客户端实例

        Returns:
            MongoClient: MongoDB同步客户端实例
        """
        return self._client

    @property
    def database(self) -> Database:
        """获取数据库实例

        Returns:
            Database: 数据库实例
        """
        if self._database is None:
            self._database = self._client[self._database_name]
        return self._database

    def get_collection(self, collection_name: str) -> Collection:
        """获取集合实例

        Args:
            collection_name: 集合名称

        Returns:
            Collection: 集合实例
        """
        return self.database[collection_name]

    def insert_one(self, collection_name: str, document: Dict[str, Any]) -> Optional[str]:
        """插入单个文档

        Args:
            collection_name: 集合名称
            document: 要插入的文档

        Returns:
            Optional[str]: 插入文档的ID，失败时返回None
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.insert_one(document)
            logger.info("MongoDB文档插入成功",
                       collection=collection_name,
                       document_id=str(result.inserted_id))
            return str(result.inserted_id)
        except Exception as e:
            logger.error("MongoDB文档插入失败",
                        collection=collection_name,
                        error=str(e))
            return None

    def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> List[str]:
        """插入多个文档

        Args:
            collection_name: 集合名称
            documents: 要插入的文档列表

        Returns:
            List[str]: 插入文档的ID列表
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.insert_many(documents)
            inserted_ids = [str(id_) for id_ in result.inserted_ids]
            logger.info("MongoDB批量文档插入成功",
                       collection=collection_name,
                       count=len(inserted_ids))
            return inserted_ids
        except Exception as e:
            logger.error("MongoDB批量文档插入失败",
                        collection=collection_name,
                        error=str(e))
            return []

    def find_one(self, collection_name: str, filter_dict: Dict[str, Any] = None,
                 projection: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """查找单个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            projection: 投影字段

        Returns:
            Optional[Dict[str, Any]]: 查找到的文档，未找到时返回None
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.find_one(filter_dict or {}, projection)
            if result:
                # 转换ObjectId为字符串
                if '_id' in result:
                    result['_id'] = str(result['_id'])
            return result
        except Exception as e:
            logger.error("MongoDB文档查找失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return None

    def find_many(self, collection_name: str, filter_dict: Dict[str, Any] = None,
                  projection: Dict[str, Any] = None, limit: int = None,
                  skip: int = None, sort: List[tuple] = None) -> List[Dict[str, Any]]:
        """查找多个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            projection: 投影字段
            limit: 限制返回数量
            skip: 跳过数量
            sort: 排序条件

        Returns:
            List[Dict[str, Any]]: 查找到的文档列表
        """
        try:
            collection = self.get_collection(collection_name)
            cursor = collection.find(filter_dict or {}, projection)

            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)

            documents = list(cursor)

            # 转换ObjectId为字符串
            for doc in documents:
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])

            return documents
        except Exception as e:
            logger.error("MongoDB文档批量查找失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return []

    def update_one(self, collection_name: str, filter_dict: Dict[str, Any],
                   update_dict: Dict[str, Any], upsert: bool = False) -> bool:
        """更新单个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容
            upsert: 是否在文档不存在时插入

        Returns:
            bool: 是否更新成功
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.update_one(filter_dict, update_dict, upsert=upsert)
            success = result.modified_count > 0 or (upsert and result.upserted_id is not None)
            if success:
                logger.info("MongoDB文档更新成功",
                           collection=collection_name,
                           modified_count=result.modified_count)
            return success
        except Exception as e:
            logger.error("MongoDB文档更新失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return False

    def update_many(self, collection_name: str, filter_dict: Dict[str, Any],
                    update_dict: Dict[str, Any]) -> int:
        """更新多个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件
            update_dict: 更新内容

        Returns:
            int: 更新的文档数量
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.update_many(filter_dict, update_dict)
            logger.info("MongoDB批量文档更新成功",
                       collection=collection_name,
                       modified_count=result.modified_count)
            return result.modified_count
        except Exception as e:
            logger.error("MongoDB批量文档更新失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return 0

    def delete_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> bool:
        """删除单个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件

        Returns:
            bool: 是否删除成功
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.delete_one(filter_dict)
            success = result.deleted_count > 0
            if success:
                logger.info("MongoDB文档删除成功",
                           collection=collection_name,
                           deleted_count=result.deleted_count)
            return success
        except Exception as e:
            logger.error("MongoDB文档删除失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return False

    def delete_many(self, collection_name: str, filter_dict: Dict[str, Any]) -> int:
        """删除多个文档

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件

        Returns:
            int: 删除的文档数量
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.delete_many(filter_dict)
            logger.info("MongoDB批量文档删除成功",
                       collection=collection_name,
                       deleted_count=result.deleted_count)
            return result.deleted_count
        except Exception as e:
            logger.error("MongoDB批量文档删除失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return 0

    def count_documents(self, collection_name: str, filter_dict: Dict[str, Any] = None) -> int:
        """统计文档数量

        Args:
            collection_name: 集合名称
            filter_dict: 查询条件

        Returns:
            int: 文档数量
        """
        try:
            collection = self.get_collection(collection_name)
            count = collection.count_documents(filter_dict or {})
            return count
        except Exception as e:
            logger.error("MongoDB文档计数失败",
                        collection=collection_name,
                        filter=filter_dict,
                        error=str(e))
            return 0

    def create_index(self, collection_name: str, keys: Union[str, List[tuple]],
                     unique: bool = False, background: bool = True) -> Optional[str]:
        """创建索引

        Args:
            collection_name: 集合名称
            keys: 索引键
            unique: 是否唯一索引
            background: 是否后台创建

        Returns:
            Optional[str]: 索引名称，失败时返回None
        """
        try:
            collection = self.get_collection(collection_name)
            index_name = collection.create_index(keys, unique=unique, background=background)
            logger.info("MongoDB索引创建成功",
                       collection=collection_name,
                       index_name=index_name)
            return index_name
        except Exception as e:
            logger.error("MongoDB索引创建失败",
                        collection=collection_name,
                        keys=keys,
                        error=str(e))
            return None

    def reset(self) -> None:
        """重置提供者状态"""
        self._database = None
