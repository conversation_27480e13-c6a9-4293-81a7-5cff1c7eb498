from pydantic_settings import BaseSettings, SettingsConfigDict
from pydantic import Field, field_validator
from typing import Dict, Any, Optional
import os
from pathlib import Path

class BaseAppConfig(BaseSettings):
    """基础配置类，所有配置类都应继承自此类"""
    
    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=Path(__file__).parent.parent.parent.parent / ".env",  # 修改为相对路径
        env_file_encoding='utf-8',
        extra="ignore"  # 允许额外的配置项
    )

    def __init__(self, **kwargs):
        super().__init__(**kwargs)



class LoggingConfig(BaseAppConfig):
    """日志配置"""
    
    LEVEL: str = Field(
        default="INFO",
        description="日志级别，可选：DEBUG, INFO, WARNING, ERROR, CRITICAL"
    )
    
    DIRECTORY: str = Field(
        default="logs",
        description="日志文件存储目录"
    )
    
    MAX_FILE_SIZE: int = Field(
        default=100 * 1024 * 1024,  # 100MB
        description="单个日志文件的最大大小（字节）"
    )
    
    BACKUP_COUNT: int = Field(
        default=30,
        description="普通日志文件保留数量"
    )
    
    ERROR_BACKUP_COUNT: int = Field(
        default=60,
        description="错误日志文件保留数量"
    )
    
    @field_validator("LEVEL")
    def validate_level(cls, v: str) -> str:
        """验证日志级别"""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是以下之一: {', '.join(valid_levels)}")
        return v.upper()


class BaseAppSettings(BaseAppConfig):
    """基础应用配置"""
    PROJECT_NAME: str = Field(default="GuGu Apex Backend", description="项目名称")
    VERSION: str = Field(default="0.1.0", description="项目版本")
    DEBUG: bool = Field(default=True, description="调试模式")
    HOST: str = Field(default="0.0.0.0", description="主机地址")
    PORT: int = Field(default=8000, description="服务端口号", ge=1, le=65535)
    DEFAULT_PAGE_SIZE: int = Field(default=10, description="默认分页大小", ge=1, le=100)
    LOGGING: LoggingConfig = Field(default_factory=LoggingConfig, description="日志配置")
