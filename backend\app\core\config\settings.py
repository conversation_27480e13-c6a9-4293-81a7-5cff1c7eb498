from pydantic import BaseModel

from app.core.config.ai_distributed import AiDistributedSettings
from app.core.config.audit import AuditSettings
from app.core.config.base import BaseAppSettings
from app.core.config.phone import PhoneSettings
from app.core.config.security import SecuritySettings
from app.core.config.database import PostgreSQLSettings, RedisSettings, MongoSettings
from app.core.config.oauth import OAuthSettings
from app.core.config.github import GitHubSettings
from app.core.config.email import EmailSettings
from app.core.config.wechat_official_account import WechatOfficialAccountSettings


class Settings(BaseModel):
    """应用总配置"""
    app: BaseAppSettings = BaseAppSettings()
    security: SecuritySettings = SecuritySettings()
    postgresql: PostgreSQLSettings = PostgreSQLSettings()
    redis: RedisSettings = RedisSettings()
    mongodb: MongoSettings = MongoSettings()
    oauth: OAuthSettings = OAuthSettings()
    wechat_official: WechatOfficialAccountSettings = WechatOfficialAccountSettings()
    github: GitHubSettings = GitHubSettings()
    email: EmailSettings = EmailSettings()
    phone: PhoneSettings = PhoneSettings()
    distributed: AiDistributedSettings = AiDistributedSettings()
    audit: AuditSettings = AuditSettings()

settings = Settings()
