#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/4/27 11:23
# @File    : statistics.py
# @Description: 
"""
import structlog


from app.api.base import BaseHandler
from dependency_injector.wiring import inject, Provide

from app.core.di import Container
from app.services.statistics import StatisticsService

logger = structlog.get_logger(__name__)


class StatisticsHandler(BaseHandler):
    """统计数据处理器"""

    @inject
    def initialize(
            self,
            statistics_service: StatisticsService = Provide[Container.statistics_service]
    ):
        """初始化处理器

        Args:
            statistics_service: 统计服务实例
        """
        super().initialize()
        self.statistics_service = statistics_service

    async def get(self) -> None:
        """获取统计数据"""
        try:
            stats = await self.statistics_service.get_statistics()
            self.success_response(stats)
        except Exception as e:
            logger.error("获取统计数据失败", error=str(e))
            self.write_error(500, error_message=f"获取统计数据失败: {str(e)}")
            return


    async def post(self) -> None:
        """增加访问计数"""
        try:
            # 默认增加visitors计数
            stat_name = self.get_argument("stat_name", "visitors")

            # 调用服务增加计数
            new_count = await self.statistics_service.increment_count(stat_name)

            self.success_response({
                "name": stat_name,
                "count": new_count
            })

        except Exception as e:
            logger.error("增加统计计数失败", error=str(e))
            self.write_error(500, error_message=f"增加统计计数失败: {str(e)}")
            return

# @require_auth(required=True, permissions=["statistics:manage"])
class StatisticsAdminHandler(BaseHandler):
    """统计数据管理处理器（需要管理员权限）"""

    @inject
    def initialize(
            self,
            statistics_service: StatisticsService = Provide[Container.statistics_service]
    ):
        """初始化处理器

        Args:
            statistics_service: 统计服务实例
        """
        super().initialize()
        self.statistics_service = statistics_service

    async def post(self) -> None:
        """创建或更新统计项"""
        try:
            if not self.json_body:
                self.write_error(400, error_message="请求体不能为空")
                return

            name = self.json_body.get("name")
            count = self.json_body.get("count")

            if not name or not isinstance(count, int):
                self.write_error(400, error_message="无效的请求参数，需要提供name和count")
                return

            # 创建或更新统计
            new_count = await self.statistics_service.create_or_update(name, count)

            self.success_response({
                "name": name,
                "count": new_count
            })
        except Exception as e:
            logger.error("管理统计数据失败", error=str(e))
            self.write_error(500, error_message=f"管理统计数据失败: {str(e)}")
            return