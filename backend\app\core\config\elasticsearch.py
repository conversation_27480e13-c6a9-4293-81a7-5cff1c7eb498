"""
Elasticsearch配置
"""
from pydantic import Field
from app.core.config.base import BaseAppConfig


class ElasticsearchSettings(BaseAppConfig):
    """Elasticsearch配置"""
    ELASTICSEARCH_HOST: str = Field(default="localhost", env="ELASTICSEARCH_HOST")
    ELASTICSEARCH_PORT: int = Field(default=9200, env="ELASTICSEARCH_PORT")

    @property
    def elasticsearch_url(self) -> str:
        """获取Elasticsearch URL"""
        return f"http://{self.ELASTICSEARCH_HOST}:{self.ELASTICSEARCH_PORT}"
