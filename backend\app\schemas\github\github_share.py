#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/16 15:55
# @File    : github_share.py
# @Description: 
"""
import json
import random
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator


class GitHubSharedCreate(BaseModel):
    """项目分享修改"""
    project_id: str = Field(..., description="项目详情描述")
    shared_pic: Optional[str] = Field(None, description="上传图片base64")
    shared_data: Optional[str] = Field(None, description="手机界面分享更新json")


    @field_validator('shared_data', mode='before')
    @classmethod
    def validate_shared_data(cls, value):
        if isinstance(value, (list, dict)):
            return json.dumps(value)
        return value


