#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/4/18 18:07
# @File    : github_project_card.py
# @Description: 
"""
from app.services.github.github_downloader import GitHubDownloader
from app.services.github.github_project import GitHubProjectService
from app.services.github.github_readme_generater import GitHubReadmeGenerate


import structlog
from typing import List, Optional, Dict, Any
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.schemas.github.github_card import GitHubCardCreate, GitHubCardUpdate, GitHubCardBase, GitHubCard
from dependency_injector.wiring import inject, Provide
from app.core.middleware import require_auth
from app.utils.security import get_current_user_id

logger = structlog.get_logger(__name__)


class GitHubCardHandler(BaseHandler):
    """GitHub项目卡片处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器
        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def get(self) -> None:
        """获取项目的所有卡片"""
        try:
            # 获取项目ID
            project_id = self.get_argument("project_id", "")
            page = int(self.get_argument("page", "1"))
            page_size = int(self.get_argument("page_size", "10"))

            # 参数校验
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 10

            auth_header = self.request.headers.get('Authorization')
            user_id = get_current_user_id(auth_header)
            # 调用服务获取卡片列表
            cards = await self.github_project_service.get_project_cards(
                project_id=project_id,
                page=page,
                page_size=page_size,
                user_id=user_id
            )

            # 返回结果
            card_list = [card.model_dump() for card in cards]

            # 返回结果
            self.success_response(card_list)
        except Exception as e:
            logger.error("获取项目卡片失败", error=str(e))
            self.write_error(500, error_message=f"获取项目卡片失败: {str(e)}")

    # @require_auth(required=True)
    async def post(self) -> None:
        """创建项目卡片"""
        try:
            # 验证请求数据
            data = GitHubCardCreate(**self.json_body)
            # 创建卡片
            card = await self.github_project_service.create_card(data)
            if not card:
                raise HTTPError(500, "创建卡片失败")
            # 返回结果
            self.success_response(card.model_dump())
        except ValueError as e:
            logger.error("创建卡片参数错误", error=str(e))
            self.write_error(500, error_message=f"创建卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("创建卡片失败", error=str(e))
            self.write_error(500, error_message=f"创建卡片失败: {str(e)}")


class GitHubCardDetailHandler(BaseHandler):
    """GitHub项目卡片详情处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def get(self, card_id: str) -> None:
        """获取卡片详情

        Args:
            card_id: 卡片ID
        """
        try:
            # 获取卡片详情
            card_id = (self.get_argument("card_id"))
            card = await self.github_project_service.get_card(card_id)
            if not card:
                self.write_error(500, error_message=f"卡片不存在: {card_id}")

            # 返回结果
            self.success_response(card.model_dump())
        except Exception as e:
            logger.error("获取卡片详情失败", error=str(e), card_id=card_id)
            self.write_error(500, error_message=f"获取卡片详情失败:")

    async def put(self):
        """更新卡片

        Args:
            card_id: 卡片ID
        """
        try:
            # 验证请求数据
            data = GitHubCard(**self.json_body)

            # 更新卡片
            card = await self.github_project_service.update_card(data)
            if not card:
                raise HTTPError(500, f"卡片不存在: {data.id}")

            # 返回结果
            self.success_response(card.model_dump())
        except ValueError as e:
            logger.error("更新卡片参数错误", error=str(e))
            self.write_error(500, error_message=f"更新卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("更新卡片失败", error=str(e), card_id=data.id)
            self.write_error(500, error_message=f"更新卡片失败: {str(e)}")

    # @require_auth(required=True)
    async def delete(self) -> None:
        """删除卡片

        Args:
            card_id: 卡片ID
        """
        try:
            # 删除卡片
            card_id = (self.get_argument("card_id"))
            success = await self.github_project_service.delete_card(card_id)
            if not success:
                self.write_error(500, error_message=f"卡片不存在:")
            # 返回结果
            self.success_response(message="卡片删除成功")
        except Exception as e:
            logger.error("删除卡片失败", error=str(e), card_id=card_id)
            self.write_error(500, error_message=f"删除卡片失败: {str(e)}")

class GitHubCardInteractionHandler(BaseHandler):
    """GitHub项目卡片互动处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub卡片服务
        """
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def post(self, card_id: str) -> None:
        # 更新卡片互动（点赞、收藏、不喜欢）
        try:

            card_id = self.json_body.get("card_id")
            action = self.json_body.get("action")
            if action not in ["like", "collect", "dislike"]:
                raise HTTPError(400, "无效的action参数")
            # 更新互动
            success = await self.github_project_service.update_card_interaction(
                card_id, action
            )
            if not success:
                raise HTTPError(404, f"卡片互动更新失败: {card_id}")

            # 返回结果
            self.success_response(message=f"卡片互动更新成功")
        except Exception as e:
            logger.error("更新卡片互动失败", error=str(e), card_id=card_id)
            self.write_error(500, error_message=f"更新卡片互动失败: {str(e)}")


class GitHubCardBatchHandler(BaseHandler):
    """GitHub项目卡片批量处理器 - 删除所有卡片并批量添加"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def post(self) -> None:
        """批量更新项目卡片（删除所有卡片并添加新卡片）"""
        try:
            # 验证请求数据
            if not self.json_body:
                raise ValueError("请求体不能为空")

            project_id = self.json_body.get("project_id")
            if not project_id:
                raise ValueError("项目ID不能为空")

            cards_data = self.json_body.get("cards", [])

            # 删除所有卡片并批量添加新卡片
            result = await self.github_project_service.batch_update_cards(project_id, cards_data)
            serializable_result = [card.dict() if hasattr(card, "dict") else card for card in result]
            self.success_response(serializable_result)

        except ValueError as e:
            logger.error("批量更新卡片参数错误", error=str(e))
            self.write_error(500, error_message=f"批量更新卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("批量更新卡片失败", error=str(e), project_id=project_id)
            self.write_error(500, error_message=f"批量更新卡片失败: {str(e)}")




class GitHubDownloadStatusHandler(BaseHandler):

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def get(self) -> None:
        """批量更新项目卡片（删除所有卡片并添加新卡片）"""
        try:
            download_status = await GitHubDownloader.get_download_queue_status()
            self.success_response(download_status)

        except ValueError as e:
            self.write_error(500, error_message=f"GitHubDownloadStatusHandler: {str(e)}")
        except Exception as e:
            self.write_error(500, error_message=f"GitHubDownloadStatusHandler: {str(e)}")


class GitHubGenerateStatusHandler(BaseHandler):

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def get(self) -> None:
        """批量更新项目卡片（删除所有卡片并添加新卡片）"""
        try:
            generate_status = await GitHubReadmeGenerate.get_generate_queue_status()
            self.success_response(generate_status)

        except ValueError as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")
        except Exception as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")


class GitHubAutoFinderHandler(BaseHandler):

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def post(self) -> None:
        try:
            count = self.json_body.get("count")
            # 调用服务获取卡片列表
            ans = await self.github_project_service.queue_unanalyzed_projects(count)
            self.success_response(ans)

        except ValueError as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")
        except Exception as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")

class GitHubSolveDataHandler(BaseHandler):

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def post(self) -> None:
        try:
            count = self.json_body.get("count")
            # 调用服务获取卡片列表
            ans = await self.github_project_service.solve_data(count)
            self.success_response(ans)

        except ValueError as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")
        except Exception as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")


class GitHubDeleteWrongCardHandler(BaseHandler):

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器"""
        super().initialize()
        self.github_project_service = github_project_service

    # @require_auth(required=True)
    async def post(self) -> None:
        """批量更新项目卡片（删除所有卡片并添加新卡片）"""
        try:
            count = self.json_body.get("count")
            # 调用服务获取卡片列表
            ans = await self.github_project_service.delete_wrong_cards_porject(count)
            self.success_response(ans)

        except ValueError as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")
        except Exception as e:
            self.write_error(500, error_message=f"GitHubGenerateStatusHandler: {str(e)}")