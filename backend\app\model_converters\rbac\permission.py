"""
权限模型转换器
"""
from typing import Optional
from app.models.rbac.permission import PermissionModel
from app.schemas.rbac.permission import Permission
from ..base import BaseConverter

class PermissionConverter(BaseConverter[PermissionModel, Permission]):
    """权限模型转换器"""
    
    def to_schema(self, model: PermissionModel) -> Permission:
        """将权限模型转换为schema
        
        Args:
            model: 权限模型实例
            
        Returns:
            权限schema实例
        """
        # 临时存储roles和group避免循环引用
        self._store_temp_attr(model, 'roles', [])
        self._store_temp_attr(model, 'group', None)
        
        # 转换基本属性
        permission = Permission.model_validate(model)
        
        # 恢复roles和group但不转换，避免循环引用
        self._restore_temp_attr(model, 'roles')
        self._restore_temp_attr(model, 'group')
        
        return permission
    
    def to_model(self, schema: Permission) -> PermissionModel:
        """将权限schema转换为模型
        
        Args:
            schema: 权限schema实例
            
        Returns:
            权限模型实例
        """
        # 转换基本属性
        permission = PermissionModel()
        for field in schema.model_fields:
            if field not in ['roles', 'group'] and hasattr(schema, field):
                value = getattr(schema, field)
                if value is not None:
                    setattr(permission, field, value)
        
        return permission
