import structlog
from typing import Dict, Any, Optional

logger = structlog.get_logger(__name__)

class AppException(Exception):
    """App基础异常类"""
    pass

class APIException(Exception):
    """API异常基类"""
    
    status_code: int = 500
    error_code: str = "internal_error"
    message: str = "服务器内部错误"
    details: Optional[Dict[str, Any]] = None
    
    def __init__(
        self,
        message: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        status_code: Optional[int] = None,
        error_code: Optional[str] = None,
    ):
        self.message = message or self.message
        self.details = details or self.details
        self.status_code = status_code or self.status_code
        self.error_code = error_code or self.error_code
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "error": {
                "code": self.error_code,
                "message": self.message,
            }
        }
        if self.details:
            result["error"]["details"] = self.details
        return result
