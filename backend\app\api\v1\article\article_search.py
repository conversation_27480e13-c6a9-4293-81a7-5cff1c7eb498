"""
文章搜索API

提供统一的文章搜索功能，支持多种过滤条件和排序选项
"""
from typing import Dict, Any, List, Optional

from app.api.base import BaseHandler
from app.services.elasticsearch.article_indexer import ArticleIndexer
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide
import structlog

logger = structlog.get_logger(__name__)


class ArticleSearchHandler(BaseHandler):
    """文章搜索处理器"""

    @inject
    def initialize(
        self,
        article_indexer: ArticleIndexer = Provide[Container.article_indexer]
    ):
        """初始化处理器

        Args:
            article_indexer: 文章 Elasticsearch搜索服务
        """
        self.indexer = article_indexer
        super().initialize()

    async def post(self):
        """高级搜索接口，支持复杂过滤条件"""
        try:
            # 解析请求体
            request_data = self.json_body
            if not request_data:
                return self.write_error(400, error_message="请求体不能为空")

            # 获取搜索参数
            query = request_data.get("query", "")
            filters = request_data.get("filters", {})
            sort_by = request_data.get("sort_by")
            sort_order = request_data.get("sort_order", "desc")
            page = request_data.get("page", 1)
            size = request_data.get("size", 10)
            highlight = request_data.get("highlight", True)

            # 执行搜索
            search_result = await self.indexer.search_articles(
                query=query,
                filters=filters,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=size,
                highlight=highlight
            )

            # 检查搜索结果是否包含错误
            if "error" in search_result:
                error_message = search_result.get("error", "未知搜索错误")
                logger.error("高级搜索失败", error=error_message)
                return self.write_error(500, error_message=f"高级搜索遇到错误: {error_message}")

            # 返回搜索结果
            return self.success_response(search_result)

        except Exception as e:
            logger.error("高级搜索失败", error=str(e), exc_info=True)
            return self.write_error(500, error_message=f"高级搜索遇到错误: {str(e)}")


class PopularArticlesHandler(BaseHandler):
    """热门文章处理器"""

    @inject
    def initialize(
        self,
        article_indexer: ArticleIndexer = Provide[Container.article_indexer]
    ):
        """初始化处理器

        Args:
            article_indexer: 文章 Elasticsearch搜索服务
        """
        self.indexer = article_indexer
        super().initialize()

    async def get(self):
        """获取热门文章"""
        try:
            # 获取参数
            limit = int(self.get_argument("limit", "10"))
            
            # 限制范围
            if limit < 1:
                limit = 10
            elif limit > 50:
                limit = 50

            # 获取热门文章
            articles = await self.indexer.get_popular_articles(limit=limit)

            return self.success_response({
                "articles": articles,
                "total": len(articles),
                "limit": limit
            })

        except Exception as e:
            logger.error("获取热门文章失败", error=str(e), exc_info=True)
            return self.write_error(500, error_message=f"获取热门文章遇到错误: {str(e)}")


class RecentArticlesHandler(BaseHandler):
    """最新文章处理器"""

    @inject
    def initialize(
        self,
        article_indexer: ArticleIndexer = Provide[Container.article_indexer]
    ):
        """初始化处理器

        Args:
            article_indexer: 文章 Elasticsearch搜索服务
        """
        self.indexer = article_indexer
        super().initialize()

    async def get(self):
        """获取最新文章"""
        try:
            # 获取参数
            limit = int(self.get_argument("limit", "10"))
            
            # 限制范围
            if limit < 1:
                limit = 10
            elif limit > 50:
                limit = 50

            # 获取最新文章
            articles = await self.indexer.get_recent_articles(limit=limit)

            return self.success_response({
                "articles": articles,
                "total": len(articles),
                "limit": limit
            })

        except Exception as e:
            logger.error("获取最新文章失败", error=str(e), exc_info=True)
            return self.write_error(500, error_message=f"获取最新文章遇到错误: {str(e)}")
