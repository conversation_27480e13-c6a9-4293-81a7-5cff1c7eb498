"""
认证中间件
实现基于JWT的用户认证和权限验证
"""
from functools import wraps
from typing import Optional, List, Type, Any, Callable
from tornado.web import RequestHandler, HTTPError
from jwt.exceptions import ExpiredSignatureError, InvalidTokenError
from dependency_injector.wiring import inject, Provide

from . import MiddlewareHandler, middleware
from app.core.di.containers import Container
from app.services.rbac.auth import AuthService
from app.services.rbac.user import UserService
import structlog

logger = structlog.get_logger(__name__)

class AuthMiddleware(MiddlewareHandler):
    """认证中间件实现"""

    def __init__(
        self,
        handler: Type[RequestHandler],
        required: bool = False,
        permissions: Optional[List[str]] = None,
    ) -> None:
        """初始化认证中间件
        
        Args:
            handler: 请求处理器类
            required: 是否必须认证
            permissions: 需要的权限列表
        """
        self.required = required
        self.permissions = permissions or []
        self.auth_service: Optional[AuthService] = None
        self.user_service: Optional[UserService] = None
        logger.debug(
            "认证中间件初始化",
            required=required,
            permissions=permissions
        )

        super().__init__(handler)

    @inject
    async def initialize(self,
                         handler: RequestHandler,
                         auth_service: AuthService=Provide[Container.auth_service],
                         user_service: UserService = Provide[Container.user_service],
                         ) -> None:

        """中间件初始化的钩子"""
        self.auth_service = auth_service
        self.user_service = user_service

    async def before_request(self, handler: RequestHandler) -> None:
        """请求处理前进行认证"""

        # 跨域处理
        if handler.request.method == "OPTIONS":
            return

        # 获取认证头
        auth_header = handler.request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            if self.required:
                logger.warning("需要认证但未提供认证令牌")
                raise HTTPError(401, "未认证")
            return

        try:
            # 解析令牌
            token = auth_header.split(" ")[1]
            
            # 使用AuthService验证访问令牌
            user_info = await self.auth_service.verify_access_token(token)
            if not user_info:
                if self.required:
                    logger.warning("无效的认证令牌")
                    raise HTTPError(401, "无效的认证令牌")


            # 这是不对的，超级管理员的bug还没有解决，其他role可能也会有丢失权限的问题？暂缓
            if user_info.is_superuser:
                return

            # 检查权限
            if self.permissions:
                # get_role_and_permission_by_id
                # user_permissions = await self.auth_service._get_role_permissions(user_info.id)
                user_info = await self.user_service.get_user_with_roles(user_info.id)

                # user_permissions_codes = {perm.code: perm for perm in user_info.get('permissions')}
                for permission in self.permissions:
                    if permission not in user_info.get('permissions'):
                        logger.warning(
                            "用户权限不足",
                        )
                        raise HTTPError(403, "权限不足")

            # 保存用户信息
            handler.current_user = user_info
            logger.debug(
                "用户认证成功",
            )
        except ExpiredSignatureError:
            logger.warning("访问令牌已过期")
            if self.required:
                raise HTTPError(401, "访问令牌已过期")
        except InvalidTokenError as e:
            logger.warning("无效的认证令牌", error=str(e))
            if self.required:
                raise HTTPError(401, "无效的认证令牌")
        except HTTPError:
            raise
        except Exception as e:
            logger.error("认证过程发生错误", error=str(e))
            if self.required:
                raise HTTPError(500, "认证失败")

class MethodAuthMiddleware(AuthMiddleware):
    """方法级别的认证中间件实现"""

    async def process_request(self, handler: RequestHandler, method: Callable, *args: Any, **kwargs: Any) -> Any:
        """处理请求"""
        await self.before_request(handler)
        return await method(handler, *args, **kwargs)

def auth_middleware(required: bool = False,
                   permissions: Optional[List[str]] = None):
    """认证中间件装饰器，用于类级别的认证
    
    Args:
        required: 是否必须认证
        permissions: 需要的权限列表
    
    Returns:
        装饰器函数
    """
    return middleware(lambda handler: AuthMiddleware(
        handler,
        required,
        permissions
    ))

def require_auth(required: bool = True,
                permissions: Optional[List[str]] = None) -> Callable:
    """方法级别的认证装饰器
    
    Args:
        required: 是否必须认证，默认为True
        permissions: 需要的权限列表
    
    Returns:
        装饰器函数
    """
    def decorator(method: Callable) -> Callable:
        @wraps(method)
        async def wrapper(handler: RequestHandler, *args: Any, **kwargs: Any) -> Any:
            auth = MethodAuthMiddleware(type(handler), required, permissions)
            await auth.initialize(handler)
            return await auth.process_request(handler, method, *args, **kwargs)
        return wrapper
    return decorator