#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/8/8 17:21
# @File    : search.py
# @Description: 
"""
# backend/app/api/v1/unified_search.py
"""
统一搜索API处理器
"""
from typing import Dict, Any
import structlog

from app.api.base import BaseHandler
from app.services.elasticsearch.unified_search import UnifiedSearchService
from app.core.di.containers import Container
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


class UnifiedSearchHandler(BaseHandler):
    """统一搜索处理器"""

    @inject
    def initialize(self,
                   unified_search_service: UnifiedSearchService = Provide[Container.unified_search_service]):
        """
        初始化处理器

        Args:
            unified_search_service: 统一搜索服务
        """
        self.search_service = unified_search_service
        super().initialize()

    async def post(self):
        """
        统一搜索接口

        请求体参数:
        {
            "query": "搜索关键词",
            "content_type": "all|projects|articles",  // 默认: "all"
            "sort_by": "_score|updated_at|created_at|read_count|collect_count|like_count|popularity_score",  // 默认: "_score"
            "sort_order": "desc|asc",  // 默认: "desc"
            "page": 1,  // 默认: 1
            "page_size": 20,  // 默认: 20, 最大: 100
            "project_ratio": 0.5,  // 项目占比，默认: 0.5
            "article_ratio": 0.5,  // 文章占比，默认: 0.5
            "filters": {  // 过滤条件（可选）
                "project_filters": {
                    "status": "published",
                    "tags": ["Python", "Web"]
                },
                "article_filters": {
                    "status": "published",
                    "is_public": true
                }
            },
            "highlight": true  // 是否高亮，默认: true
        }
        """
        try:
            # 解析请求体
            request_data = self.json_body or {}
            
            # 添加请求参数日志
            logger.info(f"收到搜索请求，参数: {request_data}")
            
            # 获取搜索参数
            query = request_data.get("query", "").strip()
            content_type = request_data.get("content_type", "all")
            sort_by = request_data.get("sort_by", "_score")
            sort_order = request_data.get("sort_order", "desc")
            page = request_data.get("page", 1)
            # 兼容 size 参数
            page_size = request_data.get("page_size", request_data.get("size", 20))
            project_ratio = request_data.get("project_ratio", 0.5)
            article_ratio = request_data.get("article_ratio", 0.5)
            filters = request_data.get("filters", {})
            highlight = request_data.get("highlight", True)
            
            # 添加参数处理日志
            logger.info(f"处理后的搜索参数: query={query}, content_type={content_type}, page_size={page_size}")
            
            # 参数验证
            if not query:
                return self.write_error(400, error_message="搜索关键词不能为空")

            # 执行统一搜索
            search_result = await self.search_service.unified_search(
                query=query,
                content_type=content_type,
                sort_by=sort_by,
                sort_order=sort_order,
                page=page,
                page_size=page_size,
                project_ratio=project_ratio,
                article_ratio=article_ratio,
                filters=filters,
                highlight=highlight
            )
            
            # 添加搜索结果日志
            # logger.info(f"搜索完成，结果: projects={len(search_result.projects)}, articles={len(search_result.articles)}, combined_results={len(getattr(search_result, 'combined_results', []))}")

            if search_result is None:
                logger.error("搜索服务返回了空结果")
                return self.write_error(500, error_message="搜索服务异常：返回空结果")

            # 格式化返回结果
            projects = search_result.projects or []
            articles = search_result.articles or []
            combined_results = getattr(search_result, "combined_results", None) or []

            # 临时 新增：根据content_type过滤combined_results中的数据
            if content_type == "projects":
                # 只保留项目类型的数据
                filtered_combined_results = []
                for item in combined_results:
                    if item.get('return_type') == 'project':
                        filtered_combined_results.append(item)
                combined_results = filtered_combined_results

                # 同时清空articles列表，确保一致性
                articles = []

                logger.info(f"项目模式过滤后: combined_results={len(combined_results)}, 移除了文章数据")

            elif content_type == "articles":
                # 只保留文章类型的数据
                filtered_combined_results = []
                for item in combined_results:
                    if item.get('return_type') == 'article':
                        filtered_combined_results.append(item)
                combined_results = filtered_combined_results

                # 同时清空projects列表，确保一致性
                projects = []

                logger.info(f"文章模式过滤后: combined_results={len(combined_results)}, 移除了项目数据")

            # 如果content_type是"all"，则保持原样不过滤

            # 添加结果处理日志
            logger.info(
                f"最终格式化结果: projects={len(projects)}, articles={len(articles)}, combined_results={len(combined_results)}")

            response_data = {
                "projects": projects,
                "articles": articles,
                "results": combined_results,
                # "total_projects": search_result.total_projects,
                # "total_articles": search_result.total_articles,
                "total_projects": len(projects),
                "total_articles": len(articles),
                "total": search_result.total,
                "page": search_result.page,
                "page_size": search_result.page_size,
                "total_pages": search_result.total_pages,
                "search_time": search_result.search_time,
                "query": search_result.query,
                "filters": search_result.filters,
                "content_type": search_result.content_type,
                "sort_by": search_result.sort_by,
                "sort_order": search_result.sort_order,
                "project_ratio": search_result.project_ratio,
                "article_ratio": search_result.article_ratio,
                "metadata": {
                    "actual_project_count": len(projects),
                    "actual_article_count": len(articles),
                    "expected_project_count": int(
                        page_size * (search_result.project_ratio if search_result.project_ratio is not None else 0)),
                    "expected_article_count": int(
                        page_size * (search_result.article_ratio if search_result.project_ratio is not None else 0))
                }
            }

            return self.success_response(response_data)

        except ValueError as e:
            logger.error("统一搜索参数错误", error=str(e))
            return self.write_error(400, error_message=f"参数错误: {str(e)}")
        except Exception as e:
            logger.error("统一搜索失败", error=str(e), exc_info=True)
            return self.write_error(500, error_message=f"搜索服务异常: {str(e)}")