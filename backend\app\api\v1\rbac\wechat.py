#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/2 17:37
# @File    : wechat.py
# @Description: 
"""
from datetime import datetime
from typing import Optional, Dict, Any

import structlog
from dependency_injector.wiring import inject, Provide
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.services.rbac.oauth import OAuthService
from app.schemas.rbac.oauth import OAuthAuthorizeRequest, OAuthCallbackRequest
from app.core.middleware import auth_middleware, require_auth
from app.services.wechat.wechat_official_account import WechatOfficialAccountService
from app.utils.security import get_current_user_id
from app.utils.wechat_util import WechatXmlUtil

logger = structlog.get_logger(__name__)


class WechatVerifyCodeHandler(BaseHandler):
    """weixin 验证码处理器"""

    @inject
    def initialize(
            self,
            wechat_official_account_service: WechatOfficialAccountService = Provide[Container.wechat_official_account_service]
    ):
        super().initialize()
        self.wechat_official_account_service = wechat_official_account_service

    async def get(self):
        """获取微信验证码"""
        try:
            # 从认证中间件获取当前用户ID
            user_id = get_current_user_id(auth_header=self.request.headers.get('Authorization'))

            # 调用service生成验证码
            vercode = self.wechat_official_account_service.generate_wechat_bind_code(user_id)

            # 返回验证码
            self.success_response({
                "code": vercode,
                "expires_in": self.wechat_official_account_service.wechat_timeout * 60,  # 转换为秒
                "message": "验证码已生成，请在微信中发送此验证码完成绑定"
            })

        except Exception as e:
            logger.error("获取微信验证码失败", error=str(e))
            raise HTTPError(500, f"获取微信验证码失败: {str(e)}")


# 获取微信认证（去掉加密验证）
class WechatOfficialAccountAuthHandler(BaseHandler):
    """微信认证处理器"""

    @inject
    def initialize(
            self,
            wechat_official_account_service: WechatOfficialAccountService = Provide[Container.wechat_official_account_service]
    ):
        super().initialize()
        self.wechat_official_account_service = wechat_official_account_service

    async def get(self):
        """处理微信的Token验证请求"""
        try:
            # 获取微信发送的参数
            signature = self.get_argument("signature", "")
            timestamp = self.get_argument("timestamp", "")
            nonce = self.get_argument("nonce", "")
            echostr = self.get_argument("echostr", "")

            logger.info("微信GET请求",
                        signature=signature,
                        timestamp=timestamp,
                        nonce=nonce,
                        echostr=echostr)

            # 验证签名
            if self._check_signature(signature, timestamp, nonce):
                logger.info("微信签名验证成功")
                self.write(echostr)
            else:
                logger.warning("微信签名验证失败")
                self.write("")

        except Exception as e:
            logger.error("微信GET请求处理失败", error=str(e))
            raise HTTPError(500, f"微信GET请求处理失败: {str(e)}")

    def _check_signature(self, signature: str, timestamp: str, nonce: str) -> bool:
        """验证微信签名

        Args:
            signature: 微信签名
            timestamp: 时间戳
            nonce: 随机数

        Returns:
            bool: 验证结果
        """
        try:
            # 获取配置的Token
            token = WechatXmlUtil.TOKEN
            logger.info("token :: " + str(token))
            # 将token、timestamp、nonce三个参数进行字典序排序
            tmp_arr = [token, timestamp, nonce]
            tmp_arr.sort()

            # 将三个参数字符串拼接成一个字符串进行sha1加密
            tmp_str = ''.join(tmp_arr)
            import hashlib
            hash_code = hashlib.sha1(tmp_str.encode('utf-8')).hexdigest()

            logger.info("签名验证",
                        token=token,
                        timestamp=timestamp,
                        nonce=nonce,
                        tmp_str=tmp_str,
                        hash_code=hash_code,
                        signature=signature)

            # 开发者获得加密后的字符串可与signature对比，标识该请求来源于微信
            return hash_code == signature

        except Exception as e:
            logger.error("签名验证失败", error=str(e))
            return False

    async def post(self):
        """处理微信消息推送"""
        try:
            # 获取微信推送的XML数据
            xml_data = self.request.body

            logger.info("收到微信POST请求",
                        content_type=self.request.headers.get('Content-Type'),
                        content_length=len(xml_data),
                        xml_preview=xml_data[:200] if xml_data else "")

            if not xml_data:
                logger.warning("POST请求体为空")
                self.write("success")
                return

            # 解析XML消息
            message_fields = WechatXmlUtil.parse_xml(xml_data.decode('utf-8'))

            if not message_fields:
                logger.warning("XML解析失败")
                self.write("success")
                return

            logger.info("消息解析成功",
                        msg_type=message_fields.get('MsgType'),
                        from_user=message_fields.get('FromUserName'),
                        to_user=message_fields.get('ToUserName'))

            # 处理不同类型的消息
            response = await self._handle_message(message_fields)

            logger.info("消息处理完成", response_length=len(response))
            self.write(response)

        except Exception as e:
            logger.error("处理微信消息推送失败", error=str(e), exc_info=True)
            # 即使出错也要返回success，避免微信重试
            self.write("success")

    async def _handle_message(self, message_fields: Dict[str, Any]) -> str:
        """处理微信消息"""
        try:
            msg_type = message_fields.get('MsgType', '')
            from_user = message_fields.get('FromUserName', '')
            to_user = message_fields.get('ToUserName', '')

            logger.info("开始处理消息", msg_type=msg_type, from_user=from_user)

            if msg_type == 'text':
                # 处理文本消息
                content = message_fields.get('Content', '')
                logger.info("处理文本消息", content=content)

                # 对每一句话都判断是否是验证码
                if self._is_verification_code(content):
                    # 是验证码，尝试绑定
                    logger.info("尝试绑定验证码", content=content)
                    response = await self._handle_verification_code(from_user, content, to_user)
                else:
                    # 不是验证码，普通回复
                    reply_content = f"收到你的消息：{content}"
                    response = WechatXmlUtil.build_text_reply(
                        to_user=from_user,
                        from_user=to_user,
                        content=reply_content
                    )
                return response

            elif msg_type == 'event':
                # 处理事件消息
                event = message_fields.get('Event', '')
                logger.info("处理事件消息", event_type=event)

                if event == 'subscribe':
                    logger.info("处理订阅事件", event_type=event)
                    response = WechatXmlUtil.build_text_reply(
                        to_user=from_user,
                        from_user=to_user,
                        content="欢迎关注咕咕客。\n前往官网guguke.com --个人资料---关联服务号--复制验证码--发送至服务号\n即可完成账号关联。"
                    )
                    return response
                elif event == 'unsubscribe':
                    # 取消关注事件
                    logger.info("用户取消关注", from_user=from_user)
                    return "success"
                else:
                    # 其他事件
                    logger.info("其他事件", event_type=event)
                    return "success"

            elif msg_type == 'image':
                # 处理图片消息
                media_id = message_fields.get('MediaId', '')
                logger.info("处理图片消息", media_id=media_id)

                # 回复图片
                response = WechatXmlUtil.build_image_reply(
                    to_user=from_user,
                    from_user=to_user,
                    media_id=media_id
                )
                return response

            else:
                # 其他类型消息
                logger.info("其他类型消息", msg_type=msg_type)
                return "success"

        except Exception as e:
            logger.error("处理消息失败", error=str(e))
            return "success"

    def _is_verification_code(self, content: str) -> bool:
        """检查是否是验证码"""
        return len(content) == 6

    async def _handle_verification_code(self, openid: str, code: str, from_user: str) -> str:
        """处理验证码消息"""
        try:
            logger.info("=== 开始处理验证码 ===", openid=openid, code=code)
            
            # 记录当前时间
            current_time = datetime.now()
            logger.info("当前时间", current_time=current_time.strftime('%Y-%m-%d %H:%M:%S'))
            
            # 记录当前存储的所有验证码
            total_codes = len(self.wechat_official_account_service.wechat_bind_codes)
            logger.info("当前存储的验证码总数", total_codes=total_codes)
            
            if total_codes == 0:
                logger.warning("系统中没有任何验证码记录")
            else:
                logger.info("=== 验证码详细信息 ===")
                for user_id, stored_data in self.wechat_official_account_service.wechat_bind_codes.items():
                    stored_code = stored_data.get('code', '')
                    expire_time = stored_data.get('expire_time')
                    expired = current_time > expire_time if expire_time else True
                    
                    logger.info("验证码记录", 
                               user_id=user_id,
                               stored_code=stored_code,
                               expire_time=expire_time.strftime('%Y-%m-%d %H:%M:%S') if expire_time else 'None',
                               is_expired=expired,
                               code_match=(stored_code == code),
                               time_valid=(current_time <= expire_time) if expire_time else False)

            # 查找匹配的验证码
            logger.info("=== 开始验证码匹配检查 ===")
            verified_user_id = None
            
            for user_id, stored_data in self.wechat_official_account_service.wechat_bind_codes.items():
                stored_code = stored_data.get('code', '')
                expire_time = stored_data.get('expire_time')
                
                logger.info("检查用户验证码",
                           user_id=user_id,
                           input_code=code,
                           stored_code=stored_code,
                           code_match=(stored_code == code))
                
                if stored_code == code:
                    logger.info("找到匹配的验证码，检查是否过期",
                               user_id=user_id,
                               expire_time=expire_time.strftime('%Y-%m-%d %H:%M:%S') if expire_time else 'None',
                               current_time=current_time.strftime('%Y-%m-%d %H:%M:%S'),
                               is_valid=(current_time <= expire_time) if expire_time else False)
                    
                    if expire_time and current_time <= expire_time:
                        verified_user_id = user_id
                        logger.info("验证码验证成功！", user_id=user_id, code=code)
                        break
                    else:
                        logger.warning("验证码已过期",
                                      user_id=user_id,
                                      expire_time=expire_time.strftime('%Y-%m-%d %H:%M:%S') if expire_time else 'None',
                                      current_time=current_time.strftime('%Y-%m-%d %H:%M:%S'))

            if not verified_user_id:
                logger.error("=== 验证码验证失败 ===",
                            input_code=code,
                            openid=openid,
                            reason="验证码不存在或已过期",
                            total_stored_codes=total_codes)
                
                response = WechatXmlUtil.build_text_reply(
                    to_user=openid,
                    from_user=from_user,
                    content="绑定 验证码无效或过期"
                )
                logger.info("返回错误响应", response_preview=response[:100])
                return response

            # 验证码有效，开始绑定流程
            logger.info("=== 开始微信绑定流程 ===", user_id=verified_user_id, openid=openid)
            
            try:
                bind_success = await self.wechat_official_account_service.bind_wechat_to_user(
                    user_id=verified_user_id,
                    openid=openid
                )
                logger.info("绑定方法调用完成", bind_success=bind_success, user_id=verified_user_id, openid=openid)
            except Exception as bind_error:
                logger.error("调用绑定方法时发生异常", 
                            error=str(bind_error),
                            user_id=verified_user_id,
                            openid=openid,
                            exc_info=True)
                bind_success = False

            if bind_success:
                logger.info("=== 绑定成功 ===", user_id=verified_user_id, openid=openid)
                
                # 清除已使用的验证码
                if verified_user_id in self.wechat_official_account_service.wechat_bind_codes:
                    del self.wechat_official_account_service._wechat_bind_codes[verified_user_id]
                    logger.info("已清除验证码", user_id=verified_user_id)
                
                response = WechatXmlUtil.build_text_reply(
                    to_user=openid,
                    from_user=from_user,
                    content="绑定成功！您的微信账号已与系统账号关联。"
                )
            else:
                logger.error("=== 绑定失败 ===", user_id=verified_user_id, openid=openid)
                response = WechatXmlUtil.build_text_reply(
                    to_user=openid,
                    from_user=from_user,
                    content="绑定失败，该微信账号可能已被其他用户绑定，请稍后重试。"
                )

            logger.info("验证码处理完成", response_preview=response[:100])
            return response

        except Exception as e:
            logger.error("=== 处理验证码时发生异常 ===", 
                        openid=openid, 
                        code=code, 
                        error=str(e),
                        exc_info=True)
            response = WechatXmlUtil.build_text_reply(
                to_user=openid,
                from_user=from_user,
                content="绑定过程中发生错误，请稍后重试。"
            )
            return response

    def set_default_headers(self):
        """设置默认响应头"""
        super().set_default_headers()
        # 设置XML响应头
        self.set_header("Content-Type", "application/xml; charset=utf-8")

