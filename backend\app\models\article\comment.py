"""
评论数据库模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, Boolean, TIMESTAMP, ForeignKey
from sqlalchemy.orm import relationship, foreign
from sqlalchemy.dialects.postgresql import JSON

from app.models.model_base import ModelBase




class CommentModel(ModelBase):
    """评论表"""
    __tablename__ = "comments"

    # 基本信息
    content = Column(Text, nullable=False, comment='评论内容')

    # 关联信息
    project_id = Column(String(64), nullable=False, comment='项目ID')

    parent_id = Column(String(64), nullable=True, comment='父评论ID，用于楼中楼回复')
    root_id = Column(String(64), nullable=True, comment='根评论ID，用于标识评论树')

    # 用户信息
    user_id = Column(String(64), nullable=False, comment='评论用户ID')
    user_name = Column(String(100), nullable=True, comment='评论用户名称')
    user_avatar = Column(String(500), nullable=True, comment='评论用户头像')
    
    # 父评论用户信息 - 新添加的字段
    parent_user_id = Column(String(64), nullable=True, comment='父评论用户ID')
    parent_user_name = Column(String(100), nullable=True, comment='父评论用户名称')
    
    reply_count = Column(Integer, nullable=False, default=0, comment='回复数')
    like_count = Column(Integer, nullable=False, default=0, comment='点赞数')

    # 状态和审核
    status = Column(String(50), nullable=False, default='pending', comment='评论状态：pending-待审核，approved-已通过，rejected-已拒绝')
    is_public = Column(Boolean, nullable=False, default=True, comment='是否公开')

    # 审核信息
    audit_result = Column(String(50), nullable=True, comment='审核结果')
    audit_reason = Column(String(500), nullable=True, comment='审核原因')
    audit_time = Column(TIMESTAMP(timezone=True), nullable=True, comment='审核时间')
    audit_by = Column(String(64), nullable=True, comment='审核人ID')

    # 扩展信息
    comment_metadata = Column(JSON, nullable=True, default=dict, comment='扩展信息，如IP地址、设备信息等')

    def __repr__(self):
        return f"<Comment {self.id} by {self.user_name}>"