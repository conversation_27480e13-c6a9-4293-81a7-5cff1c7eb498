"""
角色相关的Pydantic模型
包含角色的创建、更新和查询模型
"""
from datetime import datetime
from typing import Optional, List
from typing_extensions import Self
from pydantic import BaseModel, Field, field_validator, model_validator
from .permission import Permission
from .permission_group import PermissionGroup

class Role(BaseModel):
    """角色数据模型"""
    id: str = Field(..., description='角色ID')
    name: str = Field(..., min_length=2, max_length=100, description='角色名称')
    code: str = Field(..., min_length=2, max_length=100, pattern=r'^[a-z_]+$', description='角色标识符')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    parent_id: Optional[str] = Field(None, description='父角色ID')
    is_active: bool = Field(True, description='是否启用')
    is_system: bool = Field(False, description='是否系统角色')
    is_inherit: bool = Field(True, description='是否继承父角色权限')
    sort_order: int = Field(0, ge=0, description='排序号')
    parent: Optional['Role'] = Field(None, exclude=True, description='父角色')
    children: List['Role'] = Field(default_factory=list, exclude=True, description='子角色列表')
    permissions: List[Permission] = Field(default_factory=list, exclude=True, description='直接关联的权限列表')
    permission_groups: List[PermissionGroup] = Field(default_factory=list, exclude=True, description='直接关联的权限组列表')
    created_at: datetime = Field(..., description='创建时间')
    updated_at: datetime = Field(..., description='更新时间')
    version: str = Field('1.0.0', description='版本号')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower():
            raise ValueError('角色标识符必须是小写字母、下划线组成')
        return v

    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": "1",
                "name": "系统管理员",
                "code": "system_admin",
                "description": "系统管理员角色",
                "parent_id": None,
                "is_active": True,
                "is_system": True,
                "is_inherit": True,
                "sort_order": 0,
                "permissions": [],
                "permission_groups": [],
                "created_at": "2024-01-01T12:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z",
                "version": "1.0.0"
            }
        }

class RoleCreateRequest(BaseModel):
    """创建角色的请求数据模型"""
    name: str = Field(..., min_length=2, max_length=100, description='角色名称')
    code: str = Field(..., min_length=2, max_length=100, pattern=r'^[a-z_]+$', description='角色标识符')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    parent_id: Optional[str] = Field(None, description='父角色ID')
    is_active: bool = Field(True, description='是否启用')
    is_system: bool = Field(False, description='是否系统角色')
    is_inherit: bool = Field(True, description='是否继承父角色权限')
    sort_order: int = Field(0, ge=0, description='排序号')
    permission_ids: Optional[List[str]] = Field(None, description='权限ID列表')
    permission_group_ids: Optional[List[str]] = Field(None, description='权限组ID列表')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower():
            raise ValueError('角色标识符必须是小写字母、下划线组成')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "name": "系统管理员",
                "code": "system_admin",
                "description": "系统管理员角色",
                "parent_id": None,
                "is_active": True,
                "is_system": True,
                "is_inherit": True,
                "sort_order": 0,
                "permission_ids": ["1", "2", "3"],
                "permission_group_ids": ["1"]
            }
        }

class RoleUpdateRequest(BaseModel):
    """更新角色的请求数据模型"""
    role_id: str = Field(..., description='角色ID')
    name: Optional[str] = Field(None, min_length=2, max_length=100, description='角色名称')
    code: Optional[str] = Field(None, min_length=2, max_length=100, pattern=r'^[a-z_]+$', description='角色标识符')
    description: Optional[str] = Field(None, max_length=200, description='描述信息')
    parent_id: Optional[str] = Field(None, description='父角色ID')
    is_active: Optional[bool] = Field(None, description='是否启用')
    is_inherit: Optional[bool] = Field(None, description='是否继承父角色权限')
    sort_order: Optional[int] = Field(None, ge=0, description='排序号')
    permission_codes: Optional[List[str]] = Field(None, description='权限Codes列表')
    permission_group_ids: Optional[List[str]] = Field(None, description='权限组ID列表')

    @field_validator('code')
    def validate_code(cls, v: str) -> str:
        if not v.islower():
            raise ValueError('角色标识符必须是小写字母、下划线组成')
        return v

    class Config:
        json_schema_extra = {
            "example": {
                "role_id": "1",
                "name": "系统管理员",
                "code": "system_admin",
                "description": "系统管理员角色",
                "parent_id": None,
                "is_active": True,
                "is_inherit": True,
                "sort_order": 0,
                "permission_ids": ["1", "2", "3"],
                "permission_group_ids": ["1"]
            }
        }

class RoleListResponse(BaseModel):
    """角色列表响应"""
    total: int = Field(..., description="总数")
    roles: List[Role] = Field(..., description="角色列表")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(0, description="总页数")

    @model_validator(mode="after")
    def compute_total_pages(self) -> Self:
        """计算总页数"""    
        if self.total and self.page_size and self.total > 0 and self.page_size > 0:
            self.total_pages = (self.total + self.page_size - 1) // self.page_size
        else:
            self.total_pages = 0
        return self

    class Config:
        """配置"""
        from_attributes = True

class RolePermissionRequest(BaseModel):
    """角色权限管理请求模型"""
    role_id: str = Field(..., description="角色ID")
    permission_ids: List[str] = Field(..., description="权限ID列表")

    class Config:
        json_schema_extra = {
            "example": {
                "role_id": "1234567890",
                "permission_ids": ["1234567890", "0987654321"]
            }
        }

class RolePermissionGroupRequest(BaseModel):
    """角色权限组管理请求模型"""
    role_id: str = Field(..., description="角色ID")
    permission_group_ids: List[str] = Field(..., description="权限组ID列表")

    class Config:
        json_schema_extra = {
            "example": {
                "role_id": "1234567890",
                "permission_group_ids": ["1234567890", "0987654321"]
            }
        }

# 解决Role类的循环引用
Role.model_rebuild()
RoleListResponse.model_rebuild()