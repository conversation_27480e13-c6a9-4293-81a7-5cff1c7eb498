"""
CORS (跨域资源共享) 中间件
"""
from typing import Optional, List
from tornado.web import Request<PERSON><PERSON><PERSON>

from . import MiddlewareHandler, middleware
from app.core.config import settings
import structlog

logger = structlog.get_logger(__name__)

class CORSMiddleware(MiddlewareHandler):
    """CORS 中间件实现"""
    
    def __init__(self, handler: RequestHand<PERSON>,
                 allow_origins: Optional[List[str]] = None,
                 allow_credentials: bool = True,
                 allow_methods: Optional[List[str]] = None,
                 allow_headers: Optional[List[str]] = None) -> None:
        """
        初始化 CORS 中间件
        
        Args:
            handler: 请求处理器实例
            allow_origins: 允许的源列表
            allow_credentials: 是否允许凭证
            allow_methods: 允许的 HTTP 方法列表
            allow_headers: 允许的请求头列表
        """
        self.allow_origins = allow_origins or settings.security.CORS_ALLOW_ORIGINS
        self.allow_credentials = allow_credentials or settings.security.CORS_ALLOW_CREDENTIALS
        self.allow_methods = allow_methods or settings.security.CORS_ALLOW_METHODS
        self.allow_headers = allow_headers or settings.security.CORS_ALLOW_HEADERS
        logger.debug(
            "CORS中间件初始化",
            allow_origins=self.allow_origins,
            allow_methods=self.allow_methods,
            allow_headers=self.allow_headers,
            allow_credentials=self.allow_credentials
        )
        super().__init__(handler)

    def set_headers(self, handler: RequestHandler) -> None:
        """设置 CORS 响应头"""
        origin = handler.request.headers.get("Origin")
        
        # 检查请求源是否在允许列表中
        if origin and (origin in self.allow_origins or "*" in self.allow_origins):
            handler.set_header("Access-Control-Allow-Origin", origin)
            logger.debug(
                "CORS: 设置响应头",
                origin=origin,
                request_uri=handler.request.uri,
                request_method=handler.request.method
            )
            
            if self.allow_credentials:
                handler.set_header("Access-Control-Allow-Credentials", "true")
                logger.debug(
                    "CORS: 允许发送认证信息",
                    origin=origin
                )
            
            # 设置允许的方法
            handler.set_header("Access-Control-Allow-Methods", ", ".join(self.allow_methods))
            
            # 设置允许的请求头
            if "*" in self.allow_headers:
                requested_headers = handler.request.headers.get("Access-Control-Request-Headers")
                if requested_headers:
                    handler.set_header("Access-Control-Allow-Headers", requested_headers)
                    logger.debug(
                        "CORS: 设置自定义请求头",
                        requested_headers=requested_headers
                    )
            else:
                handler.set_header("Access-Control-Allow-Headers", ", ".join(self.allow_headers))
        else:
            logger.warning(
                "CORS: 请求源不被允许",
                origin=origin,
                allowed_origins=self.allow_origins,
                request_uri=handler.request.uri
            )

    async def before_request(self, handler: RequestHandler) -> None:
        """处理预检请求"""
        if handler.request.method == "OPTIONS":
            self.set_headers(handler)
            handler.set_status(204)
            logger.debug(
                "CORS: 处理预检请求",
                origin=handler.request.headers.get("Origin"),
                request_method=handler.request.headers.get("Access-Control-Request-Method"),
                request_headers=handler.request.headers.get("Access-Control-Request-Headers"),
                request_uri=handler.request.uri
            )
            handler.finish()

def cors_middleware(allow_origins: Optional[List[str]] = None,
                   allow_credentials: bool = True,
                   allow_methods: Optional[List[str]] = None,
                   allow_headers: Optional[List[str]] = None):
    """
    CORS 中间件装饰器
    
    Args:
        allow_origins: 允许的源列表
        allow_credentials: 是否允许凭证
        allow_methods: 允许的 HTTP 方法列表
        allow_headers: 允许的请求头列表
    
    Returns:
        装饰器函数
    """
    return middleware(lambda handler: CORSMiddleware(
        handler,
        allow_origins=allow_origins,
        allow_credentials=allow_credentials,
        allow_methods=allow_methods,
        allow_headers=allow_headers,
    ))
