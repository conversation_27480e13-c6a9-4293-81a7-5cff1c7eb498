"""
文章数据库模型
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, JSON, DateTime, Integer, Boolean, TIMESTAMP
from sqlalchemy.ext.mutable import MutableDict

from app.models.model_base import ModelBase


class ArticleModel(ModelBase):
    """文章表"""
    __tablename__ = "articles"

    # 基本信息
    title = Column(String(255), nullable=False, comment='文章标题')
    content = Column(Text, nullable=False, comment='文章内容')
    
    # 统计信息
    read_count = Column(Integer, nullable=False, default=0, comment='文章阅读量')
    comment_count = Column(Integer, nullable=False, default=0, comment='文章评论数')
    like_count = Column(Integer, nullable=False, default=0, comment='文章点赞数')
    collect_count = Column(Integer, nullable=False, default=0, comment='文章收藏数')

    # 标签和分类
    tags = Column(JSON, nullable=True, default=list, comment='文章标签')
    
    # 分享相关
    shared_count_link = Column(Integer, nullable=False, default=0, comment='分享次数')
    shared_link = Column(String(1024), nullable=True, comment='分享链接')
    
    # 关联项目
    related_projects = Column(JSON, nullable=True, default=list, comment='关联项目列表')
    
    # 图片相关
    image_urls = Column(JSON, nullable=True, default=list, comment='图片地址列表')
    cover_image = Column(String(1024), nullable=True, comment='封面图片')
    
    # 状态和权限
    status = Column(String(50), nullable=False, default='draft', comment='文章状态：draft-草稿，published-已发布，archived-已归档')
    is_public = Column(Boolean, nullable=False, default=True, comment='是否公开')
    
    # SEO相关
    summary = Column(Text, nullable=True, comment='文章摘要')
    keywords = Column(String(500), nullable=True, comment='SEO关键词')
    
    # 排序和置顶
    sort_order = Column(Integer, nullable=False, default=0, comment='排序权重')
    is_top = Column(Boolean, nullable=False, default=False, comment='是否置顶')
    
    # 发布时间
    # published_at = Column(DateTime, nullable=True, comment='发布时间')
    published_at = Column(TIMESTAMP(timezone=True), nullable=True, comment='发布时间')

    def __repr__(self):
        return f"<Article {self.title}>" 