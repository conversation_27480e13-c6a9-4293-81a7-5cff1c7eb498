"""
中间件模块

提供了一系列 Tornado 中间件装饰器，用于增强请求处理功能。
包括：
- CORS：跨域资源共享
- 安全头：HTTP 安全响应头
- 速率限制：请求频率限制
"""
from functools import wraps
from typing import Any, Callable, Optional, Type, Union
from tornado.web import RequestHandler
import structlog

logger = structlog.get_logger(__name__)

class MiddlewareHandler:
    """中间件处理基类"""
    
    def __init__(self, handler: Type[RequestHandler]) -> None:
        """
        初始化中间件处理器
        
        Args:
            handler: Tornado 请求处理器类
        """
        self.handler = handler
        self._wrap_methods()
    
    def _wrap_methods(self) -> None:
        """包装需要的处理方法"""
        self._wrap_prepare()
        self._wrap_set_default_headers()
        self._wrap_on_finish()
    
    def _wrap_prepare(self) -> None:
        """包装 prepare 方法"""
        original_prepare = getattr(self.handler, 'prepare', None)
        
        @wraps(original_prepare)
        async def wrapped_prepare(handler_instance: RequestHandler, *args: Any, **kwargs: Any) -> None:
            await self.initialize(handler_instance)
            await self.before_request(handler_instance)
            if original_prepare:
                await original_prepare(handler_instance, *args, **kwargs)
            await self.after_prepare(handler_instance)
        
        self.handler.prepare = wrapped_prepare  # type: ignore
    
    def _wrap_set_default_headers(self) -> None:
        """包装 set_default_headers 方法"""
        logger.debug("进入中间件")
        original_set_default_headers = getattr(self.handler, 'set_default_headers', None)
        
        def wrapped_set_default_headers(handler_instance: RequestHandler) -> None:
            if original_set_default_headers:
                original_set_default_headers(handler_instance)
            self.set_headers(handler_instance)
        
        self.handler.set_default_headers = wrapped_set_default_headers  # type: ignore
    
    def _wrap_on_finish(self) -> None:
        """包装 on_finish 方法"""
        original_on_finish = getattr(self.handler, 'on_finish', None)
        
        def wrapped_on_finish(handler_instance: RequestHandler) -> None:
            self.before_finish(handler_instance)
            if original_on_finish:
                original_on_finish(handler_instance)
            self.after_finish(handler_instance)
        
        self.handler.on_finish = wrapped_on_finish  # type: ignore
    
    async def initialize(self, handler: RequestHandler) -> None:
        """中间件初始化的钩子"""
        pass

    async def before_request(self, handler: RequestHandler) -> None:
        """请求处理前的钩子"""
        pass
    
    async def after_prepare(self, handler: RequestHandler) -> None:
        """prepare 后的钩子"""
        pass
    
    def set_headers(self, handler: RequestHandler) -> None:
        """设置响应头的钩子"""
        pass
    
    def before_finish(self, handler: RequestHandler) -> None:
        """完成前的钩子"""
        pass
    
    def after_finish(self, handler: RequestHandler) -> None:
        """完成后的钩子"""
        pass

def middleware(middleware_class: Type[MiddlewareHandler]) -> Callable:
    """
    中间件装饰器
    
    Args:
        middleware_class: 中间件处理器类
    
    Returns:
        装饰器函数
    """
    def decorator(handler_class: Type[RequestHandler]) -> Type[RequestHandler]:
        middleware_class(handler_class)
        return handler_class
    return decorator

from .cors import cors_middleware
from .security import security_headers_middleware
from .rate_limit import rate_limit_middleware
from .auth import auth_middleware, require_auth

__all__ = [
    "middleware",
    "MiddlewareHandler",
    "cors_middleware",
    "security_headers_middleware",
    "rate_limit_middleware",
    "auth_middleware",
    "require_auth",
]