"""SQLAlchemy 模型基类"""
from datetime import datetime, timezone
from sqlalchemy import Column, Integer, DateTime, String, event, inspect
from sqlalchemy.dialects.postgresql import TIMESTAMP
from sqlalchemy.orm import DeclarativeBase

from app.utils.uuid_generator import uuid4


class Base(DeclarativeBase):
    """所有模型的基类"""
    pass


class ModelBase(Base):
    """
    模型基类
    
    提供以下功能：
    1. 自动生成主键ID（UUID）
    2. 自动记录创建和更新时间（UTC）
    3. 自动记录创建和更新人
    4. 支持软删除
    """
    __abstract__ = True

    # 定义不触发更新时间的字段
    _skip_updated_at = {'status'}  # 添加这个类变量

    id = Column(String(64), primary_key=True, default=uuid4, comment='主键ID')
    created_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        comment='创建时间'
    )
    updated_at = Column(
        TIMESTAMP(timezone=True),
        nullable=False,
        default=lambda: datetime.now(timezone.utc),
        onupdate=lambda: datetime.now(timezone.utc),
        comment='更新时间'
    )
    created_by = Column(String(64), nullable=True, comment='创建人ID')
    updated_by = Column(String(64), nullable=True, comment='更新人ID')
    version = Column(String(64), default='1.0.0', comment='版本号')
    
    def __repr__(self):
        """返回模型的字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"


@event.listens_for(ModelBase, 'before_update', propagate=True)
def before_update(mapper, connection, target):
    """在更新之前检查是否需要更新 updated_at"""
    # 获取已更改的字段
    state = inspect(target)
    changed = set(state.attrs.keys())

    # 如果只改变了 _skip_updated_at 中的字段，不更新 updated_at
    if changed.issubset(target._skip_updated_at):
        target.updated_at = target.updated_at  # 保持原来的更新时间不变
