"""
RBAC模型转换器基类
"""
from typing import TypeVar, Generic, Any, Optional
from pydantic import BaseModel
from sqlalchemy.orm import DeclarativeMeta

ModelT = TypeVar("ModelT", bound=DeclarativeMeta)
SchemaT = TypeVar("SchemaT", bound=BaseModel)

class BaseConverter(Generic[ModelT, SchemaT]):
    """模型转换器基类"""
    
    def __init__(self):
        """初始化转换器"""
        self._temp_attrs: dict[str, Any] = {}
    
    def to_schema(self, model: ModelT) -> SchemaT:
        """将数据库模型转换为schema
        
        Args:
            model: 数据库模型实例
            
        Returns:
            转换后的schema实例
        """
        raise NotImplementedError
    
    def to_model(self, schema: SchemaT) -> ModelT:
        """将schema转换为数据库模型
        
        Args:
            schema: schema实例
            
        Returns:
            转换后的数据库模型实例
        """
        raise NotImplementedError
    
    def _store_temp_attr(self, model: ModelT, attr: str, temp_value: Any = None) -> None:
        """临时存储模型属性
        
        Args:
            model: 数据库模型实例
            attr: 属性名
            temp_value: 临时值
        """
        value = getattr(model, attr, None)
        if value is not None:
            self._temp_attrs[attr] = value
            setattr(model, attr, temp_value)
    
    def _restore_temp_attr(self, model: ModelT, attr: str) -> None:
        """恢复模型属性
        
        Args:
            model: 数据库模型实例
            attr: 属性名
        """
        if attr in self._temp_attrs:
            setattr(model, attr, self._temp_attrs[attr])
            del self._temp_attrs[attr]
