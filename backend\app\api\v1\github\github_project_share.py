#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/6/16 11:11
# @File    : github_project_share.py
# @Description: 
"""
from app.schemas.github.github_share import GitHubSharedCreate
from app.services.github.github_downloader import GitHubDownloader
from app.services.github.github_project import GitHubProjectService


import structlog
from typing import List, Optional, Dict, Any
from tornado.web import HTTPError

from app.api.base import BaseHandler
from app.core.di.containers import Container
from app.schemas.github.github_card import GitHubCardCreate, GitHubCardUpdate, GitHubCardBase, GitHubCard
from dependency_injector.wiring import inject, Provide

logger = structlog.get_logger(__name__)


class GitHubShareHandler(BaseHandler):
    """GitHub项目分享处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器
        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True)
    async def post(self) -> None:
        """分享次数加一"""
        try:
            project_id = self.json_body.get("project_id", None)
            share_type = self.json_body.get("share_type", None)
            if not project_id:
                raise ValueError("没有project_id")
            await self.github_project_service.share_project(project_id, share_type)
            self.success_response()
        except ValueError as e:
            logger.error("项目分享参数错误", error=str(e))
            self.write_error(500, error_message=f"创建卡片参数错误: {str(e)}")
        except Exception as e:
            logger.error("项目分享失败", error=str(e))
            self.write_error(500, error_message=f"创建卡片失败: {str(e)}")

class GitHubPhoneShareSolveHandler(BaseHandler):
    """GitHub项目分享处理器"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器
        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    # @require_auth(required=True)
    async def post(self) -> None:
        try:
            data = GitHubSharedCreate.model_validate(self.json_body)
            ans = await self.github_project_service.set_shared_info(data)
            self.success_response(ans)
        except ValueError as e:
            logger.error("GitHub项目分享处理参数错误", error=str(e))
            self.write_error(500, error_message=f"GitHub项目分享数据处理错误: {str(e)}")
        except Exception as e:
            logger.error("项目分享失败", error=str(e))
            self.write_error(500, error_message=f"GitHub项目分享设置失败: {str(e)}")

    async def get(self) -> None:
        """获取GitHub项目列表"""
        try:
            project_id = self.get_argument("project_id", None)
            if not project_id:
                raise ValueError("没有project_id")
            ans = await self.github_project_service.get_shared_info(project_id)
            self.success_response(ans)
        except ValueError as e:
            logger.error("请求参数无效", error=str(e))
            self.write_error(500, error_message="请求参数无效" + str(e))
        except Exception as e:
            logger.error("GitHub项目分享设置时发生错误", error=str(e))
            self.write_error(500, error_message="获取GitHub项目分享设置失败" + str(e))

